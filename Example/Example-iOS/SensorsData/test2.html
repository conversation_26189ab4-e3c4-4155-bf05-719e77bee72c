<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    
     
    
    <script type="text/javascript" charset="utf-8">
        
        
        

(function(para) {
  var p = para.sdk_url, n = para.name, w = window, d = document, s = 'script',x = null,y = null;
  w['sensorsDataAnalytic201505'] = n;
  w[n] = w[n] || function(a) {return function() {(w[n]._q = w[n]._q || []).push([a, arguments]);}};
  var ifs = ['track','quick','register','registerPage','registerOnce','trackSignup', 'trackAbtest', 'setProfile','setOnceProfile','appendProfile', 'incrementProfile', 'deleteProfile', 'unsetProfile', 'identify','login','logout','trackLink','clearAllRegister','getAppStatus'];
  for (var i = 0; i < ifs.length; i++) {
    w[n][ifs[i]] = w[n].call(null, ifs[i]);
  }
  
//setTimeout(function(){

  if (!w[n]._t) {
    x = d.createElement(s), y = d.getElementsByTagName('head')[0];
    x.async = 1;
    x.src = p;
    x.setAttribute('charset','UTF-8');
    y.appendChild(x);
    w[n].para = para;
  }



//},10000)



})({
sdk_url:'http://static.sensorsdata.cn/sdk/test/sensorsdata.full.js',
heatmap_url:'http://static.sensorsdata.cn/sdk/test/heatmap.min.js',
use_app_track:true,
  name: 'sa',
  server_url: 'http://sdk-test.cloud.sensorsdata.cn:8006/sa?project=default&token=95c73ae661f85aa0'
});
        
        sa.quick('autoTrack');
        
        
    </script>




    
    
</head>
<body>

<br/>
body区域





</body>
</html>
