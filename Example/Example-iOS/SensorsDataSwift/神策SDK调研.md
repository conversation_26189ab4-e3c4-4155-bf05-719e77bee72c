## 1. 合规步骤

### SDK 延迟初始化

Android端，客户在其用户同意 **《隐私政策》** 后，初始化 SDK 进行数据收集。具体可以参考以下步骤：

首次调用在同意隐私条款后调用 **SensorsDataAPI.startWithConfigOptions()** 初始化 SDK，此后在 Application 的 **onCreate()** 方法中主线程初始化 SDK。

```java
// Application 的 onCreate() 方法中主线程初始化 SDK
if (同意隐私条款) {
    SAConfigOptions saConfigOptions = new SAConfigOptions(SA_SERVER_URL);
    // 初始化 SDK
    SensorsDataAPI.startWithConfigOptions(this, saConfigOptions); 
}
```

```java
// 在 Activity 中同意隐私条款后初始化 SDK
if (未同意隐私条款) {
    // 隐私协议弹窗相关逻辑
    
    if(同意隐私条款) {
        // 初始化配置
        SAConfigOptions saConfigOptions = new SAConfigOptions(SA_SERVER_URL);
        // 初始化 SDK，延迟初始化场景 context 需要传递 Activity
        SensorsDataAPI.startWithConfigOptions(this, saConfigOptions); 
    }
}
```

参考链接：[客户端SDK - 合规说明 - 数据收集安全说明](https://manual.sensorsdata.cn/sa/docs/tech_sdk_client_compliance/v0300#%E6%95%B0%E6%8D%AE%E6%94%B6%E9%9B%86%E5%AE%89%E5%85%A8%E8%AF%B4%E6%98%8E)

在iOS也是做类似处理。

## 2. 全自动埋点

全埋点采集的事件包括：
- App启动（内置事件名称： `$AppStart`）
- App退出（`$AppEnd`）
- 页面浏览（`$AppViewScreen`）
- 按钮点击（`$AppClick`）

对于我们这边来说，由于神策已经自动集成了 App启动 这些事件的处理，因为我们只需要处理 **页面浏览事件** 即可。
而对按钮的自定义处理也不太现实，原因如下：
- App里面涉及的按钮非常多，每个按钮都要做自定义命名维护起来将非常麻烦
- 神策全埋点手机按钮点击事件的时候，会包含按钮所在页面、按钮的内容和按钮的类型，这样基本上能唯一缺点是点击的哪个按钮，因为也没有在进一步做自定义的意义。

如要查看这些事件的详细说明，请查看文档：[# App SDK 预置事件和预置属性](https://manual.sensorsdata.cn/sa/docs/tech_sdk_app_preset_properties/v0300)

### 2.1 iOS端接入

```objective-c
// 引入神策分析 SDK
#import <SensorsAnalyticsSDK/SensorsAnalyticsSDK.h>

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {

    // 初始化配置
    SAConfigOptions *options = [[SAConfigOptions alloc] initWithServerURL:<#数据接收地址#> launchOptions:launchOptions];
    // 开启全埋点
    options.autoTrackEventType = SensorsAnalyticsEventTypeAppStart |
                                 SensorsAnalyticsEventTypeAppEnd |
                                 SensorsAnalyticsEventTypeAppClick |
                                 SensorsAnalyticsEventTypeAppViewScreen;
#ifdef DEBUG
    // 开启 Log
    options.enableLog = YES;
#endif

	/**
	 * 其他配置，如开启可视化全埋点 
	 */
    // 初始化 SDK
    [SensorsAnalyticsSDK startWithConfigOptions:options];
	
	
    return YES;
}
```

### 2.2 Android端接入

```java
/**  
 * 初始化 Sensors Analytics SDK  
 */
 private void initSensorsDataAPI() {  
    SAConfigOptions configOptions = new SAConfigOptions(SA_SERVER_URL);  
    // 打开自动采集, 并指定追踪哪些 AutoTrack 事件  
    configOptions.setAutoTrackEventType(SensorsAnalyticsAutoTrackEventType.APP_START |  
            SensorsAnalyticsAutoTrackEventType.APP_END |  
            SensorsAnalyticsAutoTrackEventType.APP_VIEW_SCREEN |  
            SensorsAnalyticsAutoTrackEventType.APP_CLICK);  
    // 打开 crash 信息采集  
    configOptions.enableTrackAppCrash();  
    configOptions.enableLog(true);  
    //传入 SAConfigOptions 对象，初始化神策 SDK
    SensorsDataAPI.startWithConfigOptions(this, configOptions);  
}
```

### 2.3 页面映射

如果我们不对页面进行命名，默认上报的页面名称就是页面所对应的类名。而且Android和iOS两边类名风格完全不一样，上报的数据没法统一查看，另外对于运营人员查看也非常不友好（他们没法知道这个类名对应的是哪个页面）。因而，我们才需要对页面做统一的名称映射。

#### iOS 页面映射

神策有定义一个 `SAScreenAutoTracker` 协议：

```objective-c
/**
* @abstract
* 自动追踪 (AutoTrack) 中，实现该 Protocal 的 Controller 对象可以通过接口向自动采集的事件中加入属性
*
* @discussion
* 属性的约束请参考 track:withProperties:
*/

@protocol SAAutoTracker <NSObject>

@required
- (NSDictionary *)getTrackProperties;

@end

@protocol SAScreenAutoTracker <SAAutoTracker>

@optional
- (BOOL)isIgnoredAutoTrackViewScreen;
- (NSString *)getScreenUrl;

@end
```

对于页面来说，页面对应的 xxViewController 需要实现这个协议：

```objective-c
@interface UserViewController: UIViewController<SAScreenAutoTracker>
@end

@implementation UserViewController

- (NSDictionary *)getTrackProperties {
    return @{
        @"$screen_name" : @"UserPage"
    };
}

@end
```

`$screen_name`  是神策内置的属性名，表示页面名称，如果不设置，默认是用的 ViewController 的类名。

#### Android 页面映射

神策Android SDK也有相应的接口 `ScreenAutoTracker` :

```java
public interface ScreenAutoTracker {  
    /**  
     * 返回当前页面的Url  
     * 用作下个页面的referrer  
     *     * @return String  
     */    String getScreenUrl();  
  
    /**  
     * 返回自定义属性集合  
     * 我们内置了一个属性:$screen_name,代表当前页面名称, 默认情况下,该属性会采集当前Activity的CanonicalName,即:  
     * activity.getClass().getCanonicalName(), 如果想自定义页面名称, 可以在Map里put该key进行覆盖。  
     * 注意:screen_name的前面必须要要加"$"符号  
     *  
     * @return JSONObject  
     * @throws JSONException JSONException  
     */    JSONObject getTrackProperties() throws JSONException;  
}
```

对于页面来说，页面对应的 Activity 需要实现这个协议：

```java
import android.app.Activity;  
  
import com.sensorsdata.analytics.android.autotrack.core.beans.AutoTrackConstants;  
import com.sensorsdata.analytics.android.sdk.ScreenAutoTracker;  
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI;  
import org.json.JSONException;  
import org.json.JSONObject;  
  
public class UserActivity extends Activity implements ScreenAutoTracker {  
  
    @Override  
    protected void onCreate(Bundle savedInstanceState) {  
        super.onCreate(savedInstanceState);  
         
    }
  
    @Override  
    public String getScreenUrl() {  
        return "Home/UserPage";  
    }  
  
    @Override  
    public JSONObject getTrackProperties() throws JSONException {  
        JSONObject properties = new JSONObject();  
        properties.put(AutoTrackConstants.SCREEN_NAME, "UserPage");  
        return properties;  
    }  
}
```

### 2.4 困境

目前主要存在以下困境：
#### 困境一：维护非常麻烦

由于 iOS 和 Android 两边类名不一样，又需要映射到同一个页面名称，因此我们两边都需要维护类似下面一份数据结构。

iOS端维护数据：
```json
{
"iOS_class_1" : "PageName1",
"iOS_class_2" : "PageName2",
"iOS_class_n" : "PageNameN"
}
```

Android端维护数据：
```json
{
"Android_class_1" : "PageName1",
"Android_class_2" : "PageName2",
"Android_class_n" : "PageNameN"
}
```

对于新增和删除页面，我们都需要维护上面的数据结构，另位，也可能会遗漏这个工作。

#### 困境二：有一定的工作量

对于每一个页面，我们都需要实现或继承 `ScreenAutoTracker` 接口，然后返回映射的页面名称，这个工作量也是不小的，有点侮辱 “全自动” 埋点这个名头（都要手动一个一个处理了，哪来的全自动）。

### 2.5 最佳实践

