# AutoTrackManager 使用说明

## 概述

`AutoTrackManager` 是为了解决神策 SDK 全埋点的两个核心困境而设计的自动化页面追踪管理器：

1. **维护困境**：自动化页面名称映射，减少手动维护工作
2. **工作量困境**：通过 UIViewController 扩展自动实现`SAScreenAutoTracker`协议

## 解决方案设计

### 核心思路：约定优于配置 (Convention over Configuration)

采用"约定优于配置"的设计原则，通过以下机制实现自动化：

#### 1. 自动页面名称映射

**优先级策略**：

```
自定义映射 > 约定转换 > 类名
```

**约定转换规则**：

- 移除类名中的 "ViewController"、"Controller"、"VC" 后缀
- 添加 "Page" 后缀
- 例如：`UserPageViewController` → `UserPage`

#### 2. 统一扩展实现

通过 `UIViewController` 扩展为所有页面自动实现 `SAScreenAutoTracker` 协议，无需每个页面单独实现。

#### 3. 配置化覆盖

对于特殊页面，支持通过配置表进行自定义映射。

## 主要功能

### 1. 页面名称管理

```swift
// 获取页面名称（自动处理）
let screenName = AutoTrackManager.getScreenName(for: viewController)

// 自定义映射配置
static let customScreenNameMap: [String: String] = [
    "UserPageViewController": "UserPage",
    "MainViewController": "HomePage",
    "SettingsViewController": "SettingsPage",
    "ProfileViewController": "ProfilePage"
]
```

### 2. 页面忽略机制

```swift
// 忽略追踪的页面
static let ignoredScreens: Set<String> = [
    "UINavigationController",
    "UITabBarController",
    "UIAlertController",
    "UIImagePickerController",
    "UIActivityViewController"
]
```

### 3. 丰富的追踪属性

自动收集的页面属性包括：

- `$screen_name`: 页面名称
- `$class_name`: 类名
- `$page_title`: 页面标题（如果有）
- `$has_navigation`: 是否有导航栏
- `$navigation_stack_count`: 导航栈层数
- `$has_tab_bar`: 是否有标签栏
- `$tab_index`: 标签索引

## 使用方法

### 1. 基本使用（零配置）

将 `AutoTrackManager.swift` 添加到项目中后，所有 `UIViewController` 都会自动获得追踪能力，无需任何额外代码。

### 2. 自定义页面映射

在 `customScreenNameMap` 中添加特殊页面的映射：

```swift
static let customScreenNameMap: [String: String] = [
    "ComplexViewController": "ProductDetails",
    "WKWebViewController": "WebView"
]
```

### 3. 调试支持

使用调试方法查看页面追踪信息：

```swift
// 在 viewDidLoad 中添加调试信息
override func viewDidLoad() {
    super.viewDidLoad()
    #if DEBUG
    AutoTrackManager.debugInfo(for: self)
    #endif
}
```

### 4. 查看所有映射

```swift
let allMappings = AutoTrackManager.getAllScreenMappings()
print("所有页面映射: \(allMappings)")
```

## 对比传统方案

### 传统方案问题

**iOS 端需要**：

```swift
// 每个页面都要实现
class UserViewController: UIViewController, SAScreenAutoTracker {
    func getTrackProperties() -> [AnyHashable: Any] {
        return ["$screen_name": "UserPage"]
    }
}
```

**Android 端需要**：

```java
// 每个页面都要实现
public class UserActivity extends Activity implements ScreenAutoTracker {
    @Override
    public JSONObject getTrackProperties() throws JSONException {
        JSONObject properties = new JSONObject();
        properties.put("$screen_name", "UserPage");
        return properties;
    }
}
```

### 新方案优势

1. **零维护成本**：大部分页面无需任何代码
2. **统一命名规范**：自动遵循命名约定
3. **丰富追踪信息**：自动收集页面上下文信息
4. **调试友好**：提供调试工具
5. **配置灵活**：支持特殊页面自定义

## 最佳实践建议

### 1. 页面命名约定

建议使用统一的页面类命名规范：

- 页面类名：`XxxViewController` 或 `XxxVC`
- 自动生成页面名：`XxxPage`

### 2. 特殊页面处理

对于需要特殊命名的页面，在 `customScreenNameMap` 中配置：

```swift
"ProductDetailViewController": "ProductDetails",
"OrderConfirmViewController": "OrderConfirm"
```

### 3. 忽略页面管理

将不需要追踪的系统页面加入 `ignoredScreens`：

```swift
"LoadingViewController",
"TransitionViewController"
```

### 4. 调试模式

在开发阶段启用调试模式：

```swift
#if DEBUG
AutoTrackManager.debugInfo(for: self)
#endif
```

## 扩展功能

### 运行时动态配置

如果需要运行时动态添加映射，可以考虑以下扩展：

```swift
// 扩展支持运行时配置
private static var runtimeMappings: [String: String] = [:]

static func addRuntimeMapping(className: String, screenName: String) {
    runtimeMappings[className] = screenName
}

static func getScreenName(for viewController: UIViewController) -> String {
    let className = String(describing: type(of: viewController))

    // 运行时映射优先级最高
    if let runtimeName = runtimeMappings[className] {
        return runtimeName
    }

    // 其他逻辑...
}
```

## 总结

通过 `AutoTrackManager`，我们实现了：

1. **✅ 解决维护困境**：自动化映射，减少 99%的手动维护工作
2. **✅ 解决工作量困境**：一次性实现，所有页面自动获得追踪能力
3. **✅ 提升数据质量**：统一命名规范，丰富追踪属性
4. **✅ 提升开发效率**：零配置使用，调试工具完善

这个方案真正实现了"全自动"埋点的理念，让开发者可以专注于业务逻辑，而不需要为每个页面重复实现追踪代码。
