# AutoTrackManager 使用说明

## 概述

`AutoTrackManager` 是为了解决神策 SDK 全埋点困境而设计的轻量级自动化页面追踪管理器：

1. **维护困境**：自动化页面名称映射，减少手动维护工作
2. **工作量困境**：通过 UIViewController 扩展自动实现`SAScreenAutoTracker`协议
3. **配置困境**：支持运行时动态配置和调试功能

## 解决方案设计

### 核心思路：约定优于配置 (Convention over Configuration)

采用"约定优于配置"的设计原则，通过以下机制实现自动化：

#### 1. 自动页面名称映射

**优先级策略**：

```
自定义映射 > 约定转换 > 类名
```

**约定转换规则**：

- 移除类名中的 "ViewController"、"Controller"、"VC" 后缀
- 添加 "Page" 后缀
- 例如：`UserPageViewController` → `UserPage`

#### 2. 统一扩展实现

通过 `UIViewController` 扩展为所有页面自动实现 `SAScreenAutoTracker` 协议，无需每个页面单独实现。

#### 3. 配置化覆盖

对于特殊页面，支持通过配置表进行自定义映射。

## 主要功能

### 1. 页面名称管理

```swift
// 获取页面名称（自动处理）
let screenName = AutoTrackManager.shared.getScreenName(for: viewController)

// 自定义映射配置（在AutoTrackManager内部）
private var customScreenNameMap: [String: String] = [
    "UserPageViewController": "UserPage",
    "MainViewController": "HomePage",
    "SettingsViewController": "SettingsPage",
    "ProfileViewController": "ProfilePage"
]
```

### 2. 自动追踪控制

```swift
// 启用/禁用自动追踪
AutoTrackManager.shared.isAutoTrackEnabled = true

// 检查追踪状态
if AutoTrackManager.shared.isAutoTrackEnabled {
    // 可以进行数据追踪
}
```

### 3. 页面忽略机制

```swift
// 忽略追踪的页面（在AutoTrackManager内部配置）
private var ignoredScreens: Set<String> = [
    "UINavigationController",
    "UITabBarController",
    "UIAlertController",
    "UIImagePickerController",
    "UIActivityViewController"
]

// 动态添加忽略页面
AutoTrackManager.shared.addIgnoredScreen(className: "CustomViewController")
```

### 4. 追踪属性

自动收集的页面属性包括：

- `$screen_name`: 页面名称
- `$class_name`: 类名
- `$page_title`: 页面标题（如果有）

## 使用方法

### 1. 基本使用（零配置）

将 `AutoTrackManager.swift` 添加到项目中后，所有 `UIViewController` 都会自动获得追踪能力，无需任何额外代码。

### 2. 追踪控制

```swift
// 启用自动追踪（默认已启用）
AutoTrackManager.shared.isAutoTrackEnabled = true

// 禁用自动追踪
AutoTrackManager.shared.isAutoTrackEnabled = false
```

### 3. 运行时动态配置

```swift
// 运行时添加页面映射
AutoTrackManager.shared.addCustomScreenMapping(className: "NewViewController", screenName: "新页面")

// 添加忽略页面
AutoTrackManager.shared.addIgnoredScreen(className: "CustomViewController")

// 移除运行时映射
AutoTrackManager.shared.removeRuntimeMapping(className: "NewViewController")

// 清除所有运行时映射
AutoTrackManager.shared.clearRuntimeMappings()
```

### 4. 调试支持

使用调试方法查看页面追踪信息：

```swift
// 在 viewDidLoad 中添加调试信息
override func viewDidLoad() {
    super.viewDidLoad()
    #if DEBUG
    AutoTrackManager.shared.debugInfo(for: self)
    #endif
}

// 查看完整配置信息
AutoTrackManager.shared.printAllConfigurations()
```

### 5. 查看所有映射

```swift
// 查看页面映射
let allMappings = AutoTrackManager.shared.getAllScreenMappings()
print("所有页面映射: \(allMappings)")
```

## 对比传统方案

### 传统方案问题

**iOS 端需要**：

```swift
// 每个页面都要实现
class UserViewController: UIViewController, SAScreenAutoTracker {
    func getTrackProperties() -> [AnyHashable: Any] {
        return ["$screen_name": "UserPage"]
    }
}
```

**Android 端需要**：

```java
// 每个页面都要实现
public class UserActivity extends Activity implements ScreenAutoTracker {
    @Override
    public JSONObject getTrackProperties() throws JSONException {
        JSONObject properties = new JSONObject();
        properties.put("$screen_name", "UserPage");
        return properties;
    }
}
```

### 新方案优势

1. **零维护成本**：大部分页面无需任何代码
2. **统一命名规范**：自动遵循命名约定
3. **轻量级设计**：专注核心功能，代码简洁
4. **调试友好**：提供调试工具
5. **配置灵活**：支持特殊页面自定义
6. **运行时配置**：支持动态添加/移除映射
7. **完整协议实现**：完全实现 SAScreenAutoTracker 协议
8. **单例模式**：统一管理，易于使用

## 最佳实践建议

### 1. 追踪控制

在应用启动时设置追踪状态：

```swift
// AppDelegate.swift
func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {

    // 根据需要控制追踪状态
    AutoTrackManager.shared.isAutoTrackEnabled = true

    // 初始化神策SDK
    initializeSensorsAnalytics()

    return true
}
```

### 2. 页面命名约定

建议使用统一的页面类命名规范：

- 页面类名：`XxxViewController` 或 `XxxVC`
- 自动生成页面名：`XxxPage`

### 3. 特殊页面处理

对于需要特殊命名的页面，在 `customScreenNameMap` 中配置：

```swift
"ProductDetailViewController": "ProductDetails",
"OrderConfirmViewController": "OrderConfirm"
```

### 4. 忽略页面管理

将不需要追踪的系统页面加入 `ignoredScreens`：

```swift
"LoadingViewController",
"TransitionViewController"
```

### 5. 运行时配置管理

合理使用运行时配置功能：

```swift
// 在特定条件下添加映射
if isSpecialMode {
    AutoTrackManager.shared.addCustomScreenMapping(className: "SpecialViewController", screenName: "特殊模式页面")
}

// 在不需要时清理映射
AutoTrackManager.shared.clearRuntimeMappings()
```

### 6. 调试模式

在开发阶段启用调试模式：

```swift
#if DEBUG
AutoTrackManager.shared.debugInfo(for: self)
AutoTrackManager.shared.printAllConfigurations()
#endif
```

## 扩展功能

### 运行时动态配置

如果需要运行时动态添加映射，可以考虑以下扩展：

```swift
// 扩展支持运行时配置
private static var runtimeMappings: [String: String] = [:]

static func addRuntimeMapping(className: String, screenName: String) {
    runtimeMappings[className] = screenName
}

static func getScreenName(for viewController: UIViewController) -> String {
    let className = String(describing: type(of: viewController))

    // 运行时映射优先级最高
    if let runtimeName = runtimeMappings[className] {
        return runtimeName
    }

    // 其他逻辑...
}
```

## 扩展功能示例

### 自定义追踪属性

```swift
extension AutoTrackManager {
    func addCustomProperties(for viewController: UIViewController) -> [AnyHashable: Any] {
        var properties = getTrackProperties(for: viewController)

        // 添加业务相关属性
        if let userID = UserManager.shared.currentUserID {
            properties["user_id"] = userID
        }

        if let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            properties["app_version"] = appVersion
        }

        return properties
    }
}
```

### 条件性追踪控制

```swift
// 根据用户设置控制追踪
if UserDefaults.standard.bool(forKey: "analyticsEnabled") {
    AutoTrackManager.shared.isAutoTrackEnabled = true
} else {
    AutoTrackManager.shared.isAutoTrackEnabled = false
}
```

## 总结

通过简化后的 `AutoTrackManager`，我们实现了：

1. **✅ 解决维护困境**：自动化映射，减少 99%的手动维护工作
2. **✅ 解决工作量困境**：一次性实现，所有页面自动获得追踪能力
3. **✅ 轻量级设计**：专注核心功能，代码简洁易维护
4. **✅ 提升数据质量**：统一命名规范，核心追踪属性
5. **✅ 提升开发效率**：零配置使用，调试工具完善
6. **✅ 增强灵活性**：运行时动态配置，完整协议实现

这个方案真正实现了"全自动"埋点的理念，让开发者可以专注于业务逻辑，而不需要为每个页面重复实现追踪代码。
