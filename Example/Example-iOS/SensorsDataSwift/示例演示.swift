//
//  示例演示.swift
//  SensorsDataSwift
//
//  Created by Subo on 09/06/2025.
//  Copyright © 2025 Sensors Data Co., Ltd. All rights reserved.
//

import UIKit

// MARK: - 示例页面类

/**
 * 示例1：普通页面（零配置使用）
 * 类名：ProductViewController
 * 自动生成页面名：ProductPage
 */
class ProductViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "商品详情"
        
        #if DEBUG
        // 在调试模式下打印追踪信息
        AutoTrackManager.debugInfo(for: self)
        #endif
    }
}

/**
 * 示例2：有自定义映射的页面
 * 类名：UserPageViewController  
 * 自定义映射页面名：UserPage（在 customScreenNameMap 中配置）
 */
class UserPageViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "用户中心"
        
        #if DEBUG
        AutoTrackManager.debugInfo(for: self)
        #endif
    }
}

/**
 * 示例3：复杂页面名处理
 * 类名：ShoppingCartViewController
 * 自动生成页面名：ShoppingCartPage
 */
class ShoppingCartViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "购物车"
        
        #if DEBUG
        AutoTrackManager.debugInfo(for: self)
        #endif
    }
}

/**
 * 示例4：短命名页面
 * 类名：HomeVC
 * 自动生成页面名：HomePage
 */
class HomeVC: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        self.title = "首页"
        
        #if DEBUG
        AutoTrackManager.debugInfo(for: self)
        #endif
    }
}

// MARK: - 使用示例演示

/**
 * 演示控制器 - 展示不同页面的追踪效果
 */
class DemoViewController: UIViewController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        demonstrateAutoTracking()
        demonstratePrivacyCompliance()
    }
    
    private func setupUI() {
        view.backgroundColor = .white
        title = "AutoTrack 演示"
        
        // 创建按钮来跳转不同页面
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 20
        stackView.translatesAutoresizingMaskIntoConstraints = false
        
        let buttons = [
            createButton(title: "商品页面", action: #selector(showProductPage)),
            createButton(title: "用户页面", action: #selector(showUserPage)),
            createButton(title: "购物车页面", action: #selector(showShoppingCartPage)),
            createButton(title: "首页", action: #selector(showHomePage)),
            createButton(title: "查看所有映射", action: #selector(showAllMappings)),
            createButton(title: "隐私合规演示", action: #selector(showPrivacyDemo)),
            createButton(title: "运行时配置演示", action: #selector(showRuntimeConfigDemo))
        ]
        
        buttons.forEach { stackView.addArrangedSubview($0) }
        
        view.addSubview(stackView)
        NSLayoutConstraint.activate([
            stackView.centerX.constraint(equalTo: view.centerX),
            stackView.centerY.constraint(equalTo: view.centerY),
            stackView.leadingAnchor.constraint(greaterThanOrEqualTo: view.leadingAnchor, constant: 20),
            stackView.trailingAnchor.constraint(lessThanOrEqualTo: view.trailingAnchor, constant: -20)
        ])
    }
    
    private func createButton(title: String, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.backgroundColor = UIColor.systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.addTarget(self, action: action, for: .touchUpInside)
        
        // 设置按钮大小
        button.translatesAutoresizingMaskIntoConstraints = false
        button.heightAnchor.constraint(equalToConstant: 44).isActive = true
        button.widthAnchor.constraint(equalToConstant: 200).isActive = true
        
        return button
    }
    
    // MARK: - 页面跳转方法
    
    @objc private func showProductPage() {
        let vc = ProductViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc private func showUserPage() {
        let vc = UserPageViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc private func showShoppingCartPage() {
        let vc = ShoppingCartViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc private func showHomePage() {
        let vc = HomeVC()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc private func showAllMappings() {
        let mappings = AutoTrackManager.getAllScreenMappings()
        let message = mappings.map { "\($0.key) → \($0.value)" }.joined(separator: "\n")

        let alert = UIAlertController(
            title: "所有页面映射",
            message: message,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func showPrivacyDemo() {
        let alert = UIAlertController(
            title: "隐私合规演示",
            message: "演示隐私政策同意前后的追踪行为",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "同意隐私政策", style: .default) { _ in
            AutoTrackManager.setPrivacyPolicyAgreed(true)
            self.showPrivacyStatus()
        })

        alert.addAction(UIAlertAction(title: "拒绝隐私政策", style: .destructive) { _ in
            AutoTrackManager.setPrivacyPolicyAgreed(false)
            self.showPrivacyStatus()
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(alert, animated: true)
    }

    @objc private func showRuntimeConfigDemo() {
        // 演示运行时添加映射
        AutoTrackManager.addCustomScreenMapping(className: "DemoViewController", screenName: "演示页面")
        AutoTrackManager.addCustomScreenUrlMapping(className: "DemoViewController", screenUrl: "app://demo/main")

        let alert = UIAlertController(
            title: "运行时配置演示",
            message: "已添加运行时映射:\nDemoViewController → 演示页面\nURL: app://demo/main",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "查看调试信息", style: .default) { _ in
            AutoTrackManager.debugInfo(for: self)
        })

        alert.addAction(UIAlertAction(title: "查看所有配置", style: .default) { _ in
            AutoTrackManager.printAllConfigurations()
        })

        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showPrivacyStatus() {
        let canTrack = AutoTrackManager.canTrack()
        let message = canTrack ? "✅ 可以进行数据追踪" : "❌ 不能进行数据追踪"

        let alert = UIAlertController(
            title: "隐私状态",
            message: message,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // MARK: - 演示方法

    private func demonstrateAutoTracking() {
        print("\n=== AutoTrackManager 演示开始 ===\n")

        // 演示不同类型页面的命名转换
        let testViewControllers = [
            ProductViewController(),
            UserPageViewController(),
            ShoppingCartViewController(),
            HomeVC(),
            UINavigationController(),  // 应该被忽略
            UIAlertController()        // 应该被忽略
        ]

        for vc in testViewControllers {
            let className = String(describing: type(of: vc))
            let screenName = AutoTrackManager.getScreenName(for: vc)
            let isIgnored = AutoTrackManager.shouldIgnoreScreen(for: vc)
            let screenUrl = AutoTrackManager.getScreenUrl(for: vc)

            print("""
            页面类型: \(className)
            页面名称: \(screenName)
            是否忽略: \(isIgnored)
            页面URL: \(screenUrl)
            ---
            """)
        }

        print("=== 演示结束 ===\n")
    }

    private func demonstratePrivacyCompliance() {
        print("\n=== 隐私合规演示 ===\n")

        // 演示隐私政策同意前后的行为差异
        print("1. 隐私政策未同意时:")
        AutoTrackManager.setPrivacyPolicyAgreed(false)
        let testVC = ProductViewController()
        testVC.title = "测试商品"

        print("   - 可以追踪: \(AutoTrackManager.canTrack())")
        print("   - 是否忽略: \(AutoTrackManager.shouldIgnoreScreen(for: testVC))")
        print("   - 追踪属性: \(AutoTrackManager.getTrackProperties(for: testVC))")

        print("\n2. 隐私政策同意后:")
        AutoTrackManager.setPrivacyPolicyAgreed(true)

        print("   - 可以追踪: \(AutoTrackManager.canTrack())")
        print("   - 是否忽略: \(AutoTrackManager.shouldIgnoreScreen(for: testVC))")
        print("   - 追踪属性数量: \(AutoTrackManager.getTrackProperties(for: testVC).count)")

        print("\n=== 隐私合规演示结束 ===\n")
    }
}

// MARK: - 扩展功能演示

extension DemoViewController {
    
    /**
     * 演示如何在运行时获取页面追踪信息
     */
    private func demonstrateRuntimeTracking() {
        let vc = ProductViewController()
        vc.title = "测试商品页面"

        // 模拟在导航栈中
        let nav = UINavigationController(rootViewController: vc)

        let properties = AutoTrackManager.getTrackProperties(for: vc)
        print("运行时追踪属性: \(properties)")
    }

    /**
     * 演示调试功能
     */
    private func demonstrateDebugging() {
        let vc = ShoppingCartViewController()
        AutoTrackManager.debugInfo(for: vc)
    }

    /**
     * 演示运行时动态配置
     */
    private func demonstrateRuntimeConfiguration() {
        print("\n=== 运行时配置演示 ===\n")

        // 添加运行时映射
        AutoTrackManager.addCustomScreenMapping(className: "TestViewController", screenName: "测试页面")
        AutoTrackManager.addCustomScreenUrlMapping(className: "TestViewController", screenUrl: "app://test/main")

        // 查看所有映射
        print("所有页面映射:")
        let allMappings = AutoTrackManager.getAllScreenMappings()
        for (className, screenName) in allMappings {
            print("  \(className) -> \(screenName)")
        }

        print("\n所有URL映射:")
        let allUrlMappings = AutoTrackManager.getAllUrlMappings()
        for (className, url) in allUrlMappings {
            print("  \(className) -> \(url)")
        }

        // 清除运行时映射
        AutoTrackManager.clearRuntimeMappings()

        print("\n=== 运行时配置演示结束 ===\n")
    }
}

// MARK: - 使用说明注释

/*
 
 使用 AutoTrackManager 的效果演示：
 
 1. 零配置使用：
    - ProductViewController → ProductPage
    - ShoppingCartViewController → ShoppingCartPage  
    - HomeVC → HomePage
 
 2. 自定义映射：
    - UserPageViewController → UserPage (配置在 customScreenNameMap 中)
 
 3. 自动忽略：
    - UINavigationController → 自动忽略
    - UIAlertController → 自动忽略
 
 4. 丰富的追踪属性：
    - $screen_name: 页面名称
    - $class_name: 类名
    - $page_title: 页面标题
    - $has_navigation: 导航信息
    - $navigation_stack_count: 导航栈层数
    - $has_tab_bar: 标签栏信息
    - $tab_index: 标签索引
    - $is_modal: 是否模态展示
    - $modal_presentation_style: 模态展示样式
    - $view_hierarchy_level: 视图层级深度
    - $interface_orientation: 界面方向
    - $track_timestamp: 追踪时间戳
    - 等等...

 5. 隐私合规支持：
    - 支持隐私政策同意状态管理
    - 未同意隐私政策时自动停止追踪
    - 提供合规检查方法

 6. 运行时动态配置：
    - 支持运行时添加/移除页面映射
    - 支持动态URL映射配置
    - 提供配置管理方法

 7. 调试支持：
    - 使用 AutoTrackManager.debugInfo(for: self) 查看追踪信息
    - 使用 AutoTrackManager.getAllScreenMappings() 查看所有映射
    - 使用 AutoTrackManager.printAllConfigurations() 查看完整配置

 相比传统方案的优势：
 ✅ 减少 99% 的手动维护工作
 ✅ 统一的命名规范
 ✅ 丰富的页面上下文信息
 ✅ 完善的调试工具
 ✅ 灵活的配置机制
 ✅ 隐私合规支持
 ✅ 运行时动态配置
 
 */ 