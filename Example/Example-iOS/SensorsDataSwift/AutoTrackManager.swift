//
//  AutoTrackManager.swift
//  SensorsDataSwift
//
//  Created by Subo on 09/06/2025.
//  Copyright © 2025 Sensors Data Co., Ltd. All rights reserved.
//

import Foundation
import UIKit
import SensorsAnalyticsSDK

/**
 * 自动埋点管理器
 * 
 * 解决神策SDK全埋点的两个困境：
 * 1. 维护困境：自动化页面名称映射，减少手动维护
 * 2. 工作量困境：通过UIViewController扩展自动实现SAScreenAutoTracker
 */
class AutoTrackManager {
    
    // MARK: - 配置项
    
    /// 页面名称映射表 - 用于特殊页面的自定义命名
    /// 键：ViewController类名，值：页面名称
    static var customScreenNameMap: [String: String] = [
        "UserPageViewController": "UserPage",
        "MainViewController": "HomePage",
        "SettingsViewController": "SettingsPage",
        "ProfileViewController": "ProfilePage"
        // 可以根据需要添加更多自定义映射
    ]
    
    /// 忽略追踪的页面列表 - 系统类无需去追踪
    static var ignoredScreens: Set<String> = [
        "UINavigationController",
        "UITabBarController",
        "UIAlertController",
        "UIImagePickerController",
        "UIActivityViewController"
    ]
    
    // MARK: - 核心方法
    
    /**
     * 获取页面名称
     * 优先级：自定义映射 > 约定转换 > 类名
     */
    static func getScreenName(for viewController: UIViewController) -> String {
        let className = String(describing: type(of: viewController))
        
        // 1. 优先使用自定义映射
        if let customName = customScreenNameMap[className] {
            return customName
        }
        
        // 2. 使用约定转换：移除"ViewController"后缀，并转换为页面名
        return convertClassNameToScreenName(className)
    }
    
    /**
     * 根据约定将类名转换为页面名
     * 规则：
     * 1. 移除"ViewController"后缀
     * 2. 添加"Page"后缀
     * 3. 处理驼峰命名
     */
    private static func convertClassNameToScreenName(_ className: String) -> String {
        var screenName = className
        
        // 移除常见的Controller后缀
        let suffixesToRemove = ["ViewController", "Controller"]
        for suffix in suffixesToRemove {
            if screenName.hasSuffix(suffix) {
                screenName = String(screenName.dropLast(suffix.count))
                break
            }
        }
        
        // 如果处理后为空，使用原类名
        if screenName.isEmpty {
            screenName = className
        }
        
        // 添加Page后缀（如果还没有）
        if !screenName.hasSuffix("Page") {
            screenName += "Page"
        }
        
        return screenName
    }
    
    /**
     * 检查是否应该忽略该页面的追踪
     */
    static func shouldIgnoreScreen(for viewController: UIViewController) -> Bool {
        let className = String(describing: type(of: viewController))
        return ignoredScreens.contains(className)
    }
    
    /**
     * 获取页面追踪属性
     */
    static func getTrackProperties(for viewController: UIViewController) -> [AnyHashable: Any] {
        let screenName = getScreenName(for: viewController)
        let className = String(describing: type(of: viewController))
        
        var properties: [AnyHashable: Any] = [
            "$screen_name": screenName,
            "$class_name": className
        ]
        
        // 添加额外的页面信息
        if let title = viewController.title, !title.isEmpty {
            properties["$page_title"] = title
        }
        
        return properties
    }
}

// MARK: - UIViewController扩展

/**
 * 为所有UIViewController自动实现SAScreenAutoTracker协议
 * 这样就不需要每个页面单独实现了
 */
extension UIViewController {
    
    /**
     * 实现SAScreenAutoTracker协议的getTrackProperties方法
     */
    @objc public func getTrackProperties() -> [AnyHashable: Any] {
        return AutoTrackManager.getTrackProperties(for: self)
    }
    
    /**
     * 实现SAScreenAutoTracker协议的isIgnoredAutoTrackViewScreen方法
     */
    @objc public func isIgnoredAutoTrackViewScreen() -> Bool {
        return AutoTrackManager.shouldIgnoreScreen(for: self)
    }
}

// MARK: - 便利方法

extension AutoTrackManager {
    
    /**
     * 添加自定义页面映射
     * 在运行时动态添加映射关系
     */
    static func addCustomScreenMapping(className: String, screenName: String) {
        // 注意：由于customScreenNameMap是let常量，这里提供一个运行时映射的思路
        // 实际项目中可以考虑将customScreenNameMap改为var，或使用UserDefaults持久化
        print("提示：要添加映射 \(className) -> \(screenName)，请在customScreenNameMap中配置")
    }
    
    /**
     * 获取所有已配置的页面映射
     */
    static func getAllScreenMappings() -> [String: String] {
        return customScreenNameMap
    }
    
    /**
     * 打印调试信息
     */
    static func debugInfo(for viewController: UIViewController) {
        let className = String(describing: type(of: viewController))
        let screenName = getScreenName(for: viewController)
        let isIgnored = shouldIgnoreScreen(for: viewController)
        
        print("""
        === AutoTrack Debug Info ===
        Class Name: \(className)
        Screen Name: \(screenName)
        Is Ignored: \(isIgnored)
        Properties: \(getTrackProperties(for: viewController))
        ===========================
        """)
    }
}
