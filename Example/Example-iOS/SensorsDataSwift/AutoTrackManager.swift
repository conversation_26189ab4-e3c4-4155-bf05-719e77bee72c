//
//  AutoTrackManager.swift
//  SensorsDataSwift
//
//  Created by Subo on 09/06/2025.
//  Copyright © 2025 Sensors Data Co., Ltd. All rights reserved.
//

import Foundation
import UIKit
import SensorsAnalyticsSDK

/**
 * 自动埋点管理器
 */
public class AutoTrackManager {

    // MARK: - 单例

    public static let shared = AutoTrackManager()
    private init() {}

    // MARK: - Properties

    /// 是否启用自动追踪
    public var isAutoTrackEnabled = true

    // MARK: - 配置项

    /// 页面名称映射表 - 用于特殊页面的自定义命名
    /// 键：ViewController类名，值：页面名称
    private var customScreenNameMap: [String: String] = [:]

    /// 忽略追踪的页面列表 - 系统类无需去追踪
    private var ignoredScreens: Set<String> = [
        "UINavigationController",
        "UITabBarController",
        "UIAlertController",
        "UIImagePickerController",
        "UIActivityViewController",
        "UIDocumentPickerViewController",
        "UICloudSharingController",
        "UIVideoEditorController",
        "MFMailComposeViewController",
        "MFMessageComposeViewController"
    ]

    // MARK: - 核心方法

    /**
     * 获取页面名称
     * 优先级：运行时映射 > 自定义映射 > 约定转换 > 类名
     */
    func getScreenName(for viewController: UIViewController) -> String {
        let className = String(describing: type(of: viewController))

        // 1. 使用自定义映射
        if let customName = customScreenNameMap[className] {
            return customName
        }

        // 2. 使用约定转换：移除"ViewController"后缀，并转换为页面名
        return convertClassNameToScreenName(className)
    }

    /**
     * 根据约定将类名转换为页面名
     * 规则：
     * 1. 移除"ViewController"、"Controller"、"VC"后缀
     * 2. 添加"Page"后缀
     * 3. 处理驼峰命名
     */
    private func convertClassNameToScreenName(_ className: String) -> String {
        var screenName = className

        // 移除常见的Controller后缀
        let suffixesToRemove = ["ViewController", "Controller", "VC"]
        for suffix in suffixesToRemove {
            if screenName.hasSuffix(suffix) {
                screenName = String(screenName.dropLast(suffix.count))
                break
            }
        }

        // 如果处理后为空，使用原类名
        if screenName.isEmpty {
            screenName = className
        }

        // 添加Page后缀（如果还没有）
        if !screenName.hasSuffix("Page") {
            screenName += "Page"
        }

        return screenName
    }

    /**
     * 检查是否应该忽略该页面的追踪
     */
    func shouldIgnoreScreen(for viewController: UIViewController) -> Bool {
        // 如果未同意隐私政策，忽略所有追踪
        if !isAutoTrackEnabled {
            return true
        }

        let className = String(describing: type(of: viewController))
        return ignoredScreens.contains(className)
    }

    /**
     * 获取页面追踪属性
     * 收集丰富的页面上下文信息
     */
    func getTrackProperties(for viewController: UIViewController) -> [AnyHashable: Any] {
        // 如果不能追踪，返回空属性
        if !isAutoTrackEnabled {
            return [:]
        }

        let screenName = getScreenName(for: viewController)
        let className = String(describing: type(of: viewController))

        var properties: [AnyHashable: Any] = [
            "$screen_name": screenName,
            "$class_name": className
        ]

        // 添加页面标题
        if let title = viewController.title, !title.isEmpty {
            properties["$title"] = title
        }

        return properties
    }
}

// MARK: - UIViewController扩展

/**
 * 为所有UIViewController自动实现SAScreenAutoTracker协议
 * 这样就不需要每个页面单独实现了
 */
extension UIViewController: @retroactive SAScreenAutoTracker {

    /**
     * 实现SAScreenAutoTracker协议的getTrackProperties方法
     */
    @objc public func getTrackProperties() -> [AnyHashable: Any] {
        return AutoTrackManager.shared.getTrackProperties(for: self)
    }

    /**
     * 实现SAScreenAutoTracker协议的isIgnoredAutoTrackViewScreen方法
     */
    @objc public func isIgnoredAutoTrackViewScreen() -> Bool {
        return AutoTrackManager.shared.shouldIgnoreScreen(for: self)
    }
}

// MARK: - 便利方法

extension AutoTrackManager {
    /**
     * 添加自定义页面映射
     * 在运行时动态添加映射关系
     */
    func addCustomScreenMapping(className: String, screenName: String) {
        customScreenNameMap[className] = screenName
        print("✅ 已添加页面映射: \(className) -> \(screenName)")
    }

    func addIgnoredScreen(className: String) {
        ignoredScreens.insert(className)
        print("✅ 已添加忽略页面: \(className)")
    }

    /**
     * 移除运行时映射
     */
    func removeCustomScreenMapping(className: String) {
        customScreenNameMap.removeValue(forKey: className)
        print("🗑️ 已移除页面映射: \(className)")
    }

    /**
     * 获取所有已配置的页面映射
     */
    func getAllScreenMappings() -> [String: String] {
        return customScreenNameMap
    }

    /**
     * 打印调试信息
     */
    func debugInfo(for viewController: UIViewController) {
        let className = String(describing: type(of: viewController))
        let screenName = getScreenName(for: viewController)
        let isIgnored = shouldIgnoreScreen(for: viewController)

        print("""
        === AutoTrack Debug Info ===
        Class Name: \(className)
        Screen Name: \(screenName)
        Is Ignored: \(isIgnored)
        Auto Track Enabled: \(isAutoTrackEnabled)
        Properties: \(getTrackProperties(for: viewController))
        ===========================
        """)
    }

    /**
     * 打印所有配置信息
     */
    func printAllConfigurations() {
        print("""
        === AutoTrackManager 配置信息 ===
        自动追踪启用状态: \(isAutoTrackEnabled)

        自定义页面映射 (\(customScreenNameMap.count) 项):
        \(customScreenNameMap.map { "  \($0.key) -> \($0.value)" }.joined(separator: "\n"))

        忽略页面 (\(ignoredScreens.count) 项):
        \(ignoredScreens.map { "  \($0)" }.joined(separator: "\n"))
        ===============================
        """)
    }
}
