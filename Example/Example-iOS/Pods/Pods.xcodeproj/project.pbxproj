// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXAggregateTarget section */
		059942F7BE2A0DADA9A0DFB2CF6223FB /* SensorsAnalytics_Location */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 686C28665D308BB26CFAD725B1662ABA /* Build configuration list for PBXAggregateTarget "SensorsAnalytics_Location" */;
			buildPhases = (
				313ECC46A7346BD7D43F7F2828EB2865 /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
				8AFCCA7089A637670A8FEFE2ABD2E854 /* PBXTargetDependency */,
				6584ED0A489C61E6AE35854BCD9836CF /* PBXTargetDependency */,
			);
			name = SensorsAnalytics_Location;
		};
		355F2B76FA84C3A4483557736685BE09 /* SensorsAnalytics_DeviceOrientation */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 4AFD08FFD1B82D4F8E69430EA6E6782E /* Build configuration list for PBXAggregateTarget "SensorsAnalytics_DeviceOrientation" */;
			buildPhases = (
				1E4211A6886DD1B7047B65E60B753708 /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
				B20B55BDF8D889F1C87362E1434C696C /* PBXTargetDependency */,
				47309180A6462586285E096C88EA7721 /* PBXTargetDependency */,
			);
			name = SensorsAnalytics_DeviceOrientation;
		};
		89C207232F1DD5784BF33C140E6B05B3 /* SensorsAnalyticsSDK */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 894C51FA5CC2097CF51F537E0DE262D2 /* Build configuration list for PBXAggregateTarget "SensorsAnalyticsSDK" */;
			buildPhases = (
				692F3A52961A3C0D124EAB701967DDF8 /* [CP] Copy XCFrameworks */,
			);
			dependencies = (
				DBBF8EDA8B1C8B92F792D4B0BE2AFA66 /* PBXTargetDependency */,
			);
			name = SensorsAnalyticsSDK;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		443B06CA14BEFEBC50A1CE024BE39821 /* Pods-SensorsDataSwift-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 0905EABF34B10494A4E972611A04D4E5 /* Pods-SensorsDataSwift-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5EF7A85BD6E1A74B745DD1ACC94B42BC /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		6E6A3C7B9CA0B56F1DE7E517D5234F0F /* Pods-SensorsDataSwift-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 2746A05EB606D2F1B49E7EA779565E93 /* Pods-SensorsDataSwift-dummy.m */; };
		B26E3EE635C91FD7A77BF6C61C07968B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */; };
		BECD5CFFE73EF4F05B98A42F2252C06F /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 021A00505A1E5C9224921E431451C412 /* PrivacyInfo.xcprivacy */; };
		CD741AE25A7E7D44D9A0165D7E6756CE /* Pods-SensorsData-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 63DECA54A1B21D990E804DD2C784E414 /* Pods-SensorsData-dummy.m */; };
		D550DBCD3814C6CA183021E1EE84A084 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 6A3CFC302E99B2D915B19730F332C4F0 /* PrivacyInfo.xcprivacy */; };
		E94F387320030721E014C38ECECC4C1F /* Pods-SensorsData-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 7F1EE686DBC5410F01D528E9A90D8C36 /* Pods-SensorsData-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		F530D3BA8DAF96E815BB9BEEC5E27470 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 63E5BB4F9DF43661948C73BEE1AE237A /* PrivacyInfo.xcprivacy */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		141BA67937C1F58F0C4F9B5D29112E54 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DAA936DEF2DA8E0A459BBD83DAC41EF6;
			remoteInfo = "SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation";
		};
		38E665ED46B3EFC1A36ABCC0D6413C7A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 89C207232F1DD5784BF33C140E6B05B3;
			remoteInfo = SensorsAnalyticsSDK;
		};
		4C092B92128B1C12A07BE4CA804C4A92 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 89C207232F1DD5784BF33C140E6B05B3;
			remoteInfo = SensorsAnalyticsSDK;
		};
		4C588CB120BA3DC4603862FC44CC8C1D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 355F2B76FA84C3A4483557736685BE09;
			remoteInfo = SensorsAnalytics_DeviceOrientation;
		};
		95DD6A4E20FE571B8E5C47E3043EFADC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E6AE1FD19B52344FA517297A1CC9CECA;
			remoteInfo = "SensorsAnalytics_Location-SensorsAnalytics_Location";
		};
		E5BACB22E66C3B63631BBDF2A9A3D335 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 89C207232F1DD5784BF33C140E6B05B3;
			remoteInfo = SensorsAnalyticsSDK;
		};
		F2DBA73FFC6C7AB734870B9B7DFDAF65 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 059942F7BE2A0DADA9A0DFB2CF6223FB;
			remoteInfo = SensorsAnalytics_Location;
		};
		F92E67E54FC95DD89CF401E30E452C72 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 89C207232F1DD5784BF33C140E6B05B3;
			remoteInfo = SensorsAnalyticsSDK;
		};
		FFC741B92FE25A8A833C14EB087A188D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 61FA4817C4C3EE5DABE280D2D69CF5D6;
			remoteInfo = "SensorsAnalyticsSDK-SensorsAnalyticsSDK";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		021A00505A1E5C9224921E431451C412 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = SensorsAnalyticsSDK/Resources/Core/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		064A76ED41046C9FCB278D3F8C960765 /* SensorsAnalyticsSDK-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "SensorsAnalyticsSDK-xcframeworks.sh"; sourceTree = "<group>"; };
		0905EABF34B10494A4E972611A04D4E5 /* Pods-SensorsDataSwift-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-SensorsDataSwift-umbrella.h"; sourceTree = "<group>"; };
		141E93041CC755EAB536BD3197E7C0E1 /* Pods-SensorsData-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-SensorsData-Info.plist"; sourceTree = "<group>"; };
		1E59C13C69C28ED739D9F135B2DED211 /* SensorsAnalytics_DeviceOrientation.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SensorsAnalytics_DeviceOrientation.release.xcconfig; sourceTree = "<group>"; };
		1F77D23CE33FAFB43CB7CF590FDC6E0F /* ResourceBundle-SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation-Info.plist"; sourceTree = "<group>"; };
		2746A05EB606D2F1B49E7EA779565E93 /* Pods-SensorsDataSwift-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-SensorsDataSwift-dummy.m"; sourceTree = "<group>"; };
		384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		385C9A4277445AAB4B9759FC3CA57085 /* Pods-SensorsData.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-SensorsData.release.xcconfig"; sourceTree = "<group>"; };
		3D1BDA8BA24A0CDE7552BB658E33D465 /* Pods-SensorsDataSwift-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-SensorsDataSwift-frameworks.sh"; sourceTree = "<group>"; };
		414B8C534B9A8587E29E66D63CD9B6F9 /* SensorsAnalytics_Location-SensorsAnalytics_Location */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SensorsAnalytics_Location-SensorsAnalytics_Location"; path = SensorsAnalytics_Location.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		44D8328A04BCCC16F67848DCE8CDEA12 /* SensorsAnalytics_DeviceOrientation.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SensorsAnalytics_DeviceOrientation.debug.xcconfig; sourceTree = "<group>"; };
		46EA9169F99DB8D712F55D9FD6B70A91 /* Pods-SensorsData.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-SensorsData.modulemap"; sourceTree = "<group>"; };
		4B54DD1F7F79E38DCBB2E2A3881FEAE8 /* Pods-SensorsDataSwift.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-SensorsDataSwift.modulemap"; sourceTree = "<group>"; };
		4CD29DC3EE4B8E45AF670E1AFE0F06E2 /* Pods-SensorsData-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-SensorsData-frameworks.sh"; sourceTree = "<group>"; };
		53EA1965D0B7A605C8AAC0B1FC639594 /* SensorsAnalyticsSDK.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SensorsAnalyticsSDK.debug.xcconfig; sourceTree = "<group>"; };
		54FD082E4A041915FA99C4DF2DB4C1AB /* Pods-SensorsDataSwift-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-SensorsDataSwift-acknowledgements.plist"; sourceTree = "<group>"; };
		63DECA54A1B21D990E804DD2C784E414 /* Pods-SensorsData-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-SensorsData-dummy.m"; sourceTree = "<group>"; };
		63E5BB4F9DF43661948C73BEE1AE237A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = SensorsAnalytics_DeviceOrientation/Resources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		64B1087D225BA237DBD149A07DA3AF69 /* SensorsAnalytics_DeviceOrientation.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; name = SensorsAnalytics_DeviceOrientation.xcframework; path = SensorsAnalytics_DeviceOrientation/Source/SensorsAnalytics_DeviceOrientation.xcframework; sourceTree = "<group>"; };
		659634383EF797BDFE4AC3E5CF4C7088 /* Pods-SensorsDataSwift.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-SensorsDataSwift.debug.xcconfig"; sourceTree = "<group>"; };
		6A3CFC302E99B2D915B19730F332C4F0 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = SensorsAnalytics_Location/Resources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		6DAFCCD24B12A5ED46ED83B957F5274F /* SensorsAnalyticsSDK.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SensorsAnalyticsSDK.release.xcconfig; sourceTree = "<group>"; };
		6DCF1BE86C0DEE74A9314EA34BDD2917 /* Pods-SensorsDataSwift-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-SensorsDataSwift-Info.plist"; sourceTree = "<group>"; };
		72D7355502C33B25DC8E33A6E6A1EF55 /* Pods-SensorsData */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-SensorsData"; path = Pods_SensorsData.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7F1EE686DBC5410F01D528E9A90D8C36 /* Pods-SensorsData-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-SensorsData-umbrella.h"; sourceTree = "<group>"; };
		8284C80BD224252C8F59183D831A53C0 /* SensorsAnalytics_DeviceOrientation.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = SensorsAnalytics_DeviceOrientation.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		8772E3905667DD19D4154CDF58255F10 /* SensorsAnalytics_Location-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "SensorsAnalytics_Location-xcframeworks.sh"; sourceTree = "<group>"; };
		8CB34B3F3480696E96615F7DF898E2A9 /* ResourceBundle-SensorsAnalyticsSDK-SensorsAnalyticsSDK-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SensorsAnalyticsSDK-SensorsAnalyticsSDK-Info.plist"; sourceTree = "<group>"; };
		8DDB86CEAF5B1558114448466CB667EB /* Pods-SensorsData.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-SensorsData.debug.xcconfig"; sourceTree = "<group>"; };
		9461A66D180DD5C3243DA266C01FEC27 /* Pods-SensorsData-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-SensorsData-resources.sh"; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		AE05D5CED1FE2C65845F312DDD0EB024 /* Pods-SensorsDataSwift-resources.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-SensorsDataSwift-resources.sh"; sourceTree = "<group>"; };
		AE0B289E3B32EDC4FB51705D4A6F0D4F /* SensorsAnalyticsSDK.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; name = SensorsAnalyticsSDK.xcframework; path = SensorsAnalyticsSDK/Source/Core/SensorsAnalyticsSDK.xcframework; sourceTree = "<group>"; };
		B09F53F95004416CAB25932C1D3E04FB /* SensorsAnalyticsSDK-SensorsAnalyticsSDK */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SensorsAnalyticsSDK-SensorsAnalyticsSDK"; path = SensorsAnalyticsSDK.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		B2892535FCDC84B6EC0F4AEA376CB1E2 /* SensorsAnalytics_Location.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SensorsAnalytics_Location.debug.xcconfig; sourceTree = "<group>"; };
		C4B3DFEB93A7B9147CE0572F3CB0E051 /* LICENSE */ = {isa = PBXFileReference; includeInIndex = 1; path = LICENSE; sourceTree = "<group>"; };
		C7B200509C5CAFA96C7621A7D62E86E7 /* SensorsAnalytics_Location.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SensorsAnalytics_Location.release.xcconfig; sourceTree = "<group>"; };
		C8712A6C2DC2A3A3F729867AB4CD936F /* SensorsAnalytics_Location.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = SensorsAnalytics_Location.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		C922F58EED452E600B10C4255535986D /* SensorsAnalyticsSDK.podspec */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; path = SensorsAnalyticsSDK.podspec; sourceTree = "<group>"; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		CA85025B41DC6670C6F673651BCA4812 /* SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation"; path = SensorsAnalytics_DeviceOrientation.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		CBF3382F538A7CD1258A5E04E9A0C5B9 /* Pods-SensorsDataSwift.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-SensorsDataSwift.release.xcconfig"; sourceTree = "<group>"; };
		CFFDEA1D535DA0451E4B8F6D1BC245BC /* Pods-SensorsDataSwift-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-SensorsDataSwift-acknowledgements.markdown"; sourceTree = "<group>"; };
		E1B5505ACC7065ED9A5D3FDBD08CB27B /* Pods-SensorsData-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-SensorsData-acknowledgements.plist"; sourceTree = "<group>"; };
		E3D999BEEE9D9E340CDE8C649A72BAF9 /* ResourceBundle-SensorsAnalytics_Location-SensorsAnalytics_Location-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SensorsAnalytics_Location-SensorsAnalytics_Location-Info.plist"; sourceTree = "<group>"; };
		E56637558985C001FEBC352A7628317D /* Pods-SensorsDataSwift */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-SensorsDataSwift"; path = Pods_SensorsDataSwift.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		EC3EA99E7774FC1FAAB3B32E35B82E04 /* SensorsAnalytics_Location.xcframework */ = {isa = PBXFileReference; includeInIndex = 1; name = SensorsAnalytics_Location.xcframework; path = SensorsAnalytics_Location/Source/SensorsAnalytics_Location.xcframework; sourceTree = "<group>"; };
		F939D7955F04E72711346CAD67B42824 /* SensorsAnalytics_DeviceOrientation-xcframeworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "SensorsAnalytics_DeviceOrientation-xcframeworks.sh"; sourceTree = "<group>"; };
		FF9EF0F83E6DC39442302D1A6EBC806E /* Pods-SensorsData-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-SensorsData-acknowledgements.markdown"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		06429570762311A77DC0F291DCCCFF58 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		6099BD92441CD34A57B9DABEF0E87F89 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5EF7A85BD6E1A74B745DD1ACC94B42BC /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CC0030F6531FC15623C080A1A96F78E7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D6183A301082C5BDB51F59C6009B3DB0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F1F5F4365B8475015A4EF421BEAFECB0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B26E3EE635C91FD7A77BF6C61C07968B /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		01CDC0DF0726DBB97CBE9BA63EFB009C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				AE0B289E3B32EDC4FB51705D4A6F0D4F /* SensorsAnalyticsSDK.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		036862E69268C762A9C26A0F69BEACA2 /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				E281A2AA05309140CEC39AC5C0A75F5E /* Pods-SensorsData */,
				4C24B48BA3FEDC1CB20602DDB3E61EF6 /* Pods-SensorsDataSwift */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		2580437B3D9DC2F88F527F1617CF29CC /* SensorsAnalyticsSDK */ = {
			isa = PBXGroup;
			children = (
				6C1F8C26DC3006A490BF90DAC1E35F8B /* Core */,
				B614D2043D35A6B8082E92B2A710B643 /* Pod */,
				E4566363BB416DECE81D0E8C66BEC4D6 /* Support Files */,
			);
			name = SensorsAnalyticsSDK;
			path = ../../..;
			sourceTree = "<group>";
		};
		2A69AA0EB2D68D16D6A5E3B4C76E1F4D /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				EC3EA99E7774FC1FAAB3B32E35B82E04 /* SensorsAnalytics_Location.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		3883DFC0E66A2285720297F7B9986DDD /* SensorsAnalytics_DeviceOrientation */ = {
			isa = PBXGroup;
			children = (
				82E541AEA19AD7805F8DD0A7075C0E57 /* Core */,
				C37929FF531F67F3DBD8DA73FA97A85F /* Pod */,
				F71BA2C3747167D7F595B9F670877B16 /* Support Files */,
			);
			name = SensorsAnalytics_DeviceOrientation;
			path = ../../..;
			sourceTree = "<group>";
		};
		4C24B48BA3FEDC1CB20602DDB3E61EF6 /* Pods-SensorsDataSwift */ = {
			isa = PBXGroup;
			children = (
				4B54DD1F7F79E38DCBB2E2A3881FEAE8 /* Pods-SensorsDataSwift.modulemap */,
				CFFDEA1D535DA0451E4B8F6D1BC245BC /* Pods-SensorsDataSwift-acknowledgements.markdown */,
				54FD082E4A041915FA99C4DF2DB4C1AB /* Pods-SensorsDataSwift-acknowledgements.plist */,
				2746A05EB606D2F1B49E7EA779565E93 /* Pods-SensorsDataSwift-dummy.m */,
				3D1BDA8BA24A0CDE7552BB658E33D465 /* Pods-SensorsDataSwift-frameworks.sh */,
				6DCF1BE86C0DEE74A9314EA34BDD2917 /* Pods-SensorsDataSwift-Info.plist */,
				AE05D5CED1FE2C65845F312DDD0EB024 /* Pods-SensorsDataSwift-resources.sh */,
				0905EABF34B10494A4E972611A04D4E5 /* Pods-SensorsDataSwift-umbrella.h */,
				659634383EF797BDFE4AC3E5CF4C7088 /* Pods-SensorsDataSwift.debug.xcconfig */,
				CBF3382F538A7CD1258A5E04E9A0C5B9 /* Pods-SensorsDataSwift.release.xcconfig */,
			);
			name = "Pods-SensorsDataSwift";
			path = "Target Support Files/Pods-SensorsDataSwift";
			sourceTree = "<group>";
		};
		63ACC06717DE185C3E2B5A67807EBABA /* Products */ = {
			isa = PBXGroup;
			children = (
				72D7355502C33B25DC8E33A6E6A1EF55 /* Pods-SensorsData */,
				E56637558985C001FEBC352A7628317D /* Pods-SensorsDataSwift */,
				CA85025B41DC6670C6F673651BCA4812 /* SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation */,
				414B8C534B9A8587E29E66D63CD9B6F9 /* SensorsAnalytics_Location-SensorsAnalytics_Location */,
				B09F53F95004416CAB25932C1D3E04FB /* SensorsAnalyticsSDK-SensorsAnalyticsSDK */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6C1F8C26DC3006A490BF90DAC1E35F8B /* Core */ = {
			isa = PBXGroup;
			children = (
				021A00505A1E5C9224921E431451C412 /* PrivacyInfo.xcprivacy */,
				01CDC0DF0726DBB97CBE9BA63EFB009C /* Frameworks */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		807590413766BBE338E907A47C234C95 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				E3D999BEEE9D9E340CDE8C649A72BAF9 /* ResourceBundle-SensorsAnalytics_Location-SensorsAnalytics_Location-Info.plist */,
				8772E3905667DD19D4154CDF58255F10 /* SensorsAnalytics_Location-xcframeworks.sh */,
				B2892535FCDC84B6EC0F4AEA376CB1E2 /* SensorsAnalytics_Location.debug.xcconfig */,
				C7B200509C5CAFA96C7621A7D62E86E7 /* SensorsAnalytics_Location.release.xcconfig */,
			);
			name = "Support Files";
			path = "Example/Example-iOS/Pods/Target Support Files/SensorsAnalytics_Location";
			sourceTree = "<group>";
		};
		82E541AEA19AD7805F8DD0A7075C0E57 /* Core */ = {
			isa = PBXGroup;
			children = (
				63E5BB4F9DF43661948C73BEE1AE237A /* PrivacyInfo.xcprivacy */,
				8F7F2A3A2226EA1BB68A303B75806B28 /* Frameworks */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		88DDF42DCBC04E7854F339ABEE1FFB8C /* SensorsAnalytics_Location */ = {
			isa = PBXGroup;
			children = (
				BA6443D057F13C0E9088F8043815F858 /* Core */,
				C34BF013A99B5D367CB67003229DFF23 /* Pod */,
				807590413766BBE338E907A47C234C95 /* Support Files */,
			);
			name = SensorsAnalytics_Location;
			path = ../../..;
			sourceTree = "<group>";
		};
		8F7F2A3A2226EA1BB68A303B75806B28 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				64B1087D225BA237DBD149A07DA3AF69 /* SensorsAnalytics_DeviceOrientation.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B614D2043D35A6B8082E92B2A710B643 /* Pod */ = {
			isa = PBXGroup;
			children = (
				C4B3DFEB93A7B9147CE0572F3CB0E051 /* LICENSE */,
				C922F58EED452E600B10C4255535986D /* SensorsAnalyticsSDK.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		BA6443D057F13C0E9088F8043815F858 /* Core */ = {
			isa = PBXGroup;
			children = (
				6A3CFC302E99B2D915B19730F332C4F0 /* PrivacyInfo.xcprivacy */,
				2A69AA0EB2D68D16D6A5E3B4C76E1F4D /* Frameworks */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		C34BF013A99B5D367CB67003229DFF23 /* Pod */ = {
			isa = PBXGroup;
			children = (
				C8712A6C2DC2A3A3F729867AB4CD936F /* SensorsAnalytics_Location.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		C37929FF531F67F3DBD8DA73FA97A85F /* Pod */ = {
			isa = PBXGroup;
			children = (
				8284C80BD224252C8F59183D831A53C0 /* SensorsAnalytics_DeviceOrientation.podspec */,
			);
			name = Pod;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				ED6030EB71E36B444BF97EF9D28ADE3F /* Development Pods */,
				D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */,
				63ACC06717DE185C3E2B5A67807EBABA /* Products */,
				036862E69268C762A9C26A0F69BEACA2 /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D210D550F4EA176C3123ED886F8F87F5 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E4801F62A6B08CD9B5410329F1A18FDE /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		E281A2AA05309140CEC39AC5C0A75F5E /* Pods-SensorsData */ = {
			isa = PBXGroup;
			children = (
				46EA9169F99DB8D712F55D9FD6B70A91 /* Pods-SensorsData.modulemap */,
				FF9EF0F83E6DC39442302D1A6EBC806E /* Pods-SensorsData-acknowledgements.markdown */,
				E1B5505ACC7065ED9A5D3FDBD08CB27B /* Pods-SensorsData-acknowledgements.plist */,
				63DECA54A1B21D990E804DD2C784E414 /* Pods-SensorsData-dummy.m */,
				4CD29DC3EE4B8E45AF670E1AFE0F06E2 /* Pods-SensorsData-frameworks.sh */,
				141E93041CC755EAB536BD3197E7C0E1 /* Pods-SensorsData-Info.plist */,
				9461A66D180DD5C3243DA266C01FEC27 /* Pods-SensorsData-resources.sh */,
				7F1EE686DBC5410F01D528E9A90D8C36 /* Pods-SensorsData-umbrella.h */,
				8DDB86CEAF5B1558114448466CB667EB /* Pods-SensorsData.debug.xcconfig */,
				385C9A4277445AAB4B9759FC3CA57085 /* Pods-SensorsData.release.xcconfig */,
			);
			name = "Pods-SensorsData";
			path = "Target Support Files/Pods-SensorsData";
			sourceTree = "<group>";
		};
		E4566363BB416DECE81D0E8C66BEC4D6 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				8CB34B3F3480696E96615F7DF898E2A9 /* ResourceBundle-SensorsAnalyticsSDK-SensorsAnalyticsSDK-Info.plist */,
				064A76ED41046C9FCB278D3F8C960765 /* SensorsAnalyticsSDK-xcframeworks.sh */,
				53EA1965D0B7A605C8AAC0B1FC639594 /* SensorsAnalyticsSDK.debug.xcconfig */,
				6DAFCCD24B12A5ED46ED83B957F5274F /* SensorsAnalyticsSDK.release.xcconfig */,
			);
			name = "Support Files";
			path = "Example/Example-iOS/Pods/Target Support Files/SensorsAnalyticsSDK";
			sourceTree = "<group>";
		};
		E4801F62A6B08CD9B5410329F1A18FDE /* iOS */ = {
			isa = PBXGroup;
			children = (
				384DDA2CB25005BD6479B5987C619DD4 /* Foundation.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		ED6030EB71E36B444BF97EF9D28ADE3F /* Development Pods */ = {
			isa = PBXGroup;
			children = (
				3883DFC0E66A2285720297F7B9986DDD /* SensorsAnalytics_DeviceOrientation */,
				88DDF42DCBC04E7854F339ABEE1FFB8C /* SensorsAnalytics_Location */,
				2580437B3D9DC2F88F527F1617CF29CC /* SensorsAnalyticsSDK */,
			);
			name = "Development Pods";
			sourceTree = "<group>";
		};
		F71BA2C3747167D7F595B9F670877B16 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				1F77D23CE33FAFB43CB7CF590FDC6E0F /* ResourceBundle-SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation-Info.plist */,
				F939D7955F04E72711346CAD67B42824 /* SensorsAnalytics_DeviceOrientation-xcframeworks.sh */,
				44D8328A04BCCC16F67848DCE8CDEA12 /* SensorsAnalytics_DeviceOrientation.debug.xcconfig */,
				1E59C13C69C28ED739D9F135B2DED211 /* SensorsAnalytics_DeviceOrientation.release.xcconfig */,
			);
			name = "Support Files";
			path = "Example/Example-iOS/Pods/Target Support Files/SensorsAnalytics_DeviceOrientation";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		1ED566FE24FE6730883B565C5C60F5E9 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				443B06CA14BEFEBC50A1CE024BE39821 /* Pods-SensorsDataSwift-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2F359DDCEDE29A961D1F33E0C26F4A40 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E94F387320030721E014C38ECECC4C1F /* Pods-SensorsData-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		08BE9A72B92E27B0173746193AD90087 /* Pods-SensorsDataSwift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6C4DCACF8619B5618D8C2DD7025E3B17 /* Build configuration list for PBXNativeTarget "Pods-SensorsDataSwift" */;
			buildPhases = (
				1ED566FE24FE6730883B565C5C60F5E9 /* Headers */,
				CBE6A7B4AA1F0105A983E97C61459BE7 /* Sources */,
				6099BD92441CD34A57B9DABEF0E87F89 /* Frameworks */,
				8007AAC89FB5CB0DBAA1C5BD944E17B1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8D1FA208EC46CC770B8B0A50E97D266A /* PBXTargetDependency */,
			);
			name = "Pods-SensorsDataSwift";
			productName = Pods_SensorsDataSwift;
			productReference = E56637558985C001FEBC352A7628317D /* Pods-SensorsDataSwift */;
			productType = "com.apple.product-type.framework";
		};
		61FA4817C4C3EE5DABE280D2D69CF5D6 /* SensorsAnalyticsSDK-SensorsAnalyticsSDK */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 465CED29B777299A9DD40166B09856DD /* Build configuration list for PBXNativeTarget "SensorsAnalyticsSDK-SensorsAnalyticsSDK" */;
			buildPhases = (
				D1320D5CF2D801AC9813AE508D0E9C9B /* Sources */,
				06429570762311A77DC0F291DCCCFF58 /* Frameworks */,
				190287D57CA5BCADFD91A28D8B590624 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SensorsAnalyticsSDK-SensorsAnalyticsSDK";
			productName = SensorsAnalyticsSDK;
			productReference = B09F53F95004416CAB25932C1D3E04FB /* SensorsAnalyticsSDK-SensorsAnalyticsSDK */;
			productType = "com.apple.product-type.bundle";
		};
		B9896D1934A2B7A526AEAEBF47E30680 /* Pods-SensorsData */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2AC68A15A3B4AFB0AAD18D7885097234 /* Build configuration list for PBXNativeTarget "Pods-SensorsData" */;
			buildPhases = (
				2F359DDCEDE29A961D1F33E0C26F4A40 /* Headers */,
				23359318E2FC32D6DD633E30D40D321A /* Sources */,
				F1F5F4365B8475015A4EF421BEAFECB0 /* Frameworks */,
				DD13CD36C1687B18481FC924E5C8D490 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9465F52B16BEC608F843D60A1F2B280F /* PBXTargetDependency */,
				79437D117EDD0EDFABFF6C07D8F0F471 /* PBXTargetDependency */,
				FC733023C6354EBDDCD665B9F1935138 /* PBXTargetDependency */,
			);
			name = "Pods-SensorsData";
			productName = Pods_SensorsData;
			productReference = 72D7355502C33B25DC8E33A6E6A1EF55 /* Pods-SensorsData */;
			productType = "com.apple.product-type.framework";
		};
		DAA936DEF2DA8E0A459BBD83DAC41EF6 /* SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A6A271C89892EBBA04C3C897AB504D70 /* Build configuration list for PBXNativeTarget "SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation" */;
			buildPhases = (
				26E0C86035A6D3D35E8B506D4873D543 /* Sources */,
				D6183A301082C5BDB51F59C6009B3DB0 /* Frameworks */,
				31A400E9930A5DC0AED68A6651A5BF47 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation";
			productName = SensorsAnalytics_DeviceOrientation;
			productReference = CA85025B41DC6670C6F673651BCA4812 /* SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation */;
			productType = "com.apple.product-type.bundle";
		};
		E6AE1FD19B52344FA517297A1CC9CECA /* SensorsAnalytics_Location-SensorsAnalytics_Location */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 22DAF9DC1A2FDF6CFF4EF5E07F613EFE /* Build configuration list for PBXNativeTarget "SensorsAnalytics_Location-SensorsAnalytics_Location" */;
			buildPhases = (
				28A129D653A110329584346DA96D4D4E /* Sources */,
				CC0030F6531FC15623C080A1A96F78E7 /* Frameworks */,
				DEF0A35ADFC2F24CEBD802B21C5EDC14 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SensorsAnalytics_Location-SensorsAnalytics_Location";
			productName = SensorsAnalytics_Location;
			productReference = 414B8C534B9A8587E29E66D63CD9B6F9 /* SensorsAnalytics_Location-SensorsAnalytics_Location */;
			productType = "com.apple.product-type.bundle";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 63ACC06717DE185C3E2B5A67807EBABA /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B9896D1934A2B7A526AEAEBF47E30680 /* Pods-SensorsData */,
				08BE9A72B92E27B0173746193AD90087 /* Pods-SensorsDataSwift */,
				355F2B76FA84C3A4483557736685BE09 /* SensorsAnalytics_DeviceOrientation */,
				DAA936DEF2DA8E0A459BBD83DAC41EF6 /* SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation */,
				059942F7BE2A0DADA9A0DFB2CF6223FB /* SensorsAnalytics_Location */,
				E6AE1FD19B52344FA517297A1CC9CECA /* SensorsAnalytics_Location-SensorsAnalytics_Location */,
				89C207232F1DD5784BF33C140E6B05B3 /* SensorsAnalyticsSDK */,
				61FA4817C4C3EE5DABE280D2D69CF5D6 /* SensorsAnalyticsSDK-SensorsAnalyticsSDK */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		190287D57CA5BCADFD91A28D8B590624 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BECD5CFFE73EF4F05B98A42F2252C06F /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		31A400E9930A5DC0AED68A6651A5BF47 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F530D3BA8DAF96E815BB9BEEC5E27470 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8007AAC89FB5CB0DBAA1C5BD944E17B1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DD13CD36C1687B18481FC924E5C8D490 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DEF0A35ADFC2F24CEBD802B21C5EDC14 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D550DBCD3814C6CA183021E1EE84A084 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1E4211A6886DD1B7047B65E60B753708 /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/SensorsAnalytics_DeviceOrientation/SensorsAnalytics_DeviceOrientation-xcframeworks-input-files.xcfilelist",
			);
			name = "[CP] Copy XCFrameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/SensorsAnalytics_DeviceOrientation/SensorsAnalytics_DeviceOrientation-xcframeworks-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/SensorsAnalytics_DeviceOrientation/SensorsAnalytics_DeviceOrientation-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		313ECC46A7346BD7D43F7F2828EB2865 /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/SensorsAnalytics_Location/SensorsAnalytics_Location-xcframeworks-input-files.xcfilelist",
			);
			name = "[CP] Copy XCFrameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/SensorsAnalytics_Location/SensorsAnalytics_Location-xcframeworks-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/SensorsAnalytics_Location/SensorsAnalytics_Location-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		692F3A52961A3C0D124EAB701967DDF8 /* [CP] Copy XCFrameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/SensorsAnalyticsSDK/SensorsAnalyticsSDK-xcframeworks-input-files.xcfilelist",
			);
			name = "[CP] Copy XCFrameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/SensorsAnalyticsSDK/SensorsAnalyticsSDK-xcframeworks-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/SensorsAnalyticsSDK/SensorsAnalyticsSDK-xcframeworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		23359318E2FC32D6DD633E30D40D321A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CD741AE25A7E7D44D9A0165D7E6756CE /* Pods-SensorsData-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		26E0C86035A6D3D35E8B506D4873D543 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		28A129D653A110329584346DA96D4D4E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CBE6A7B4AA1F0105A983E97C61459BE7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6E6A3C7B9CA0B56F1DE7E517D5234F0F /* Pods-SensorsDataSwift-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D1320D5CF2D801AC9813AE508D0E9C9B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		47309180A6462586285E096C88EA7721 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SensorsAnalyticsSDK;
			target = 89C207232F1DD5784BF33C140E6B05B3 /* SensorsAnalyticsSDK */;
			targetProxy = 4C092B92128B1C12A07BE4CA804C4A92 /* PBXContainerItemProxy */;
		};
		6584ED0A489C61E6AE35854BCD9836CF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SensorsAnalyticsSDK;
			target = 89C207232F1DD5784BF33C140E6B05B3 /* SensorsAnalyticsSDK */;
			targetProxy = 38E665ED46B3EFC1A36ABCC0D6413C7A /* PBXContainerItemProxy */;
		};
		79437D117EDD0EDFABFF6C07D8F0F471 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SensorsAnalytics_DeviceOrientation;
			target = 355F2B76FA84C3A4483557736685BE09 /* SensorsAnalytics_DeviceOrientation */;
			targetProxy = 4C588CB120BA3DC4603862FC44CC8C1D /* PBXContainerItemProxy */;
		};
		8AFCCA7089A637670A8FEFE2ABD2E854 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SensorsAnalytics_Location-SensorsAnalytics_Location";
			target = E6AE1FD19B52344FA517297A1CC9CECA /* SensorsAnalytics_Location-SensorsAnalytics_Location */;
			targetProxy = 95DD6A4E20FE571B8E5C47E3043EFADC /* PBXContainerItemProxy */;
		};
		8D1FA208EC46CC770B8B0A50E97D266A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SensorsAnalyticsSDK;
			target = 89C207232F1DD5784BF33C140E6B05B3 /* SensorsAnalyticsSDK */;
			targetProxy = E5BACB22E66C3B63631BBDF2A9A3D335 /* PBXContainerItemProxy */;
		};
		9465F52B16BEC608F843D60A1F2B280F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SensorsAnalyticsSDK;
			target = 89C207232F1DD5784BF33C140E6B05B3 /* SensorsAnalyticsSDK */;
			targetProxy = F92E67E54FC95DD89CF401E30E452C72 /* PBXContainerItemProxy */;
		};
		B20B55BDF8D889F1C87362E1434C696C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation";
			target = DAA936DEF2DA8E0A459BBD83DAC41EF6 /* SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation */;
			targetProxy = 141BA67937C1F58F0C4F9B5D29112E54 /* PBXContainerItemProxy */;
		};
		DBBF8EDA8B1C8B92F792D4B0BE2AFA66 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SensorsAnalyticsSDK-SensorsAnalyticsSDK";
			target = 61FA4817C4C3EE5DABE280D2D69CF5D6 /* SensorsAnalyticsSDK-SensorsAnalyticsSDK */;
			targetProxy = FFC741B92FE25A8A833C14EB087A188D /* PBXContainerItemProxy */;
		};
		FC733023C6354EBDDCD665B9F1935138 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SensorsAnalytics_Location;
			target = 059942F7BE2A0DADA9A0DFB2CF6223FB /* SensorsAnalytics_Location */;
			targetProxy = F2DBA73FFC6C7AB734870B9B7DFDAF65 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0FAAFA1606DC4D90105442161648EDD9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8DDB86CEAF5B1558114448466CB667EB /* Pods-SensorsData.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-SensorsData/Pods-SensorsData-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-SensorsData/Pods-SensorsData.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		25AD9454612BF454A1E3DC4CD4FA8C6D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		2D45D91400B224B01714B8DB8DDA3D81 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CBF3382F538A7CD1258A5E04E9A0C5B9 /* Pods-SensorsDataSwift.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-SensorsDataSwift/Pods-SensorsDataSwift-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-SensorsDataSwift/Pods-SensorsDataSwift.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		47683FCAA5591A69CA67535AC8734FBA /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 44D8328A04BCCC16F67848DCE8CDEA12 /* SensorsAnalytics_DeviceOrientation.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		5088F9FFA17EBA9A4E178008F727FE89 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6DAFCCD24B12A5ED46ED83B957F5274F /* SensorsAnalyticsSDK.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SensorsAnalyticsSDK";
				IBSC_MODULE = SensorsAnalyticsSDK;
				INFOPLIST_FILE = "Target Support Files/SensorsAnalyticsSDK/ResourceBundle-SensorsAnalyticsSDK-SensorsAnalyticsSDK-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SensorsAnalyticsSDK;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		5F34359A53D7C9EF3BD3DF9D5D5EDB1D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 385C9A4277445AAB4B9759FC3CA57085 /* Pods-SensorsData.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-SensorsData/Pods-SensorsData-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-SensorsData/Pods-SensorsData.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		6CD9018A7BCC9E7CBF72BF706768AEF7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 53EA1965D0B7A605C8AAC0B1FC639594 /* SensorsAnalyticsSDK.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SensorsAnalyticsSDK";
				IBSC_MODULE = SensorsAnalyticsSDK;
				INFOPLIST_FILE = "Target Support Files/SensorsAnalyticsSDK/ResourceBundle-SensorsAnalyticsSDK-SensorsAnalyticsSDK-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SensorsAnalyticsSDK;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		7E6A009C1C5FCC5BFF8AF3F495563D9B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C7B200509C5CAFA96C7621A7D62E86E7 /* SensorsAnalytics_Location.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		9D3C5FF72F57C693BE96C23761A44BBB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E59C13C69C28ED739D9F135B2DED211 /* SensorsAnalytics_DeviceOrientation.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SensorsAnalytics_DeviceOrientation";
				IBSC_MODULE = SensorsAnalytics_DeviceOrientation;
				INFOPLIST_FILE = "Target Support Files/SensorsAnalytics_DeviceOrientation/ResourceBundle-SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SensorsAnalytics_DeviceOrientation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		A0A0342EFDF0E50B733EB53F35CDBE95 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C7B200509C5CAFA96C7621A7D62E86E7 /* SensorsAnalytics_Location.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SensorsAnalytics_Location";
				IBSC_MODULE = SensorsAnalytics_Location;
				INFOPLIST_FILE = "Target Support Files/SensorsAnalytics_Location/ResourceBundle-SensorsAnalytics_Location-SensorsAnalytics_Location-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SensorsAnalytics_Location;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		C9E8D0EC18366C68FC70AD34874C0CA8 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 44D8328A04BCCC16F67848DCE8CDEA12 /* SensorsAnalytics_DeviceOrientation.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SensorsAnalytics_DeviceOrientation";
				IBSC_MODULE = SensorsAnalytics_DeviceOrientation;
				INFOPLIST_FILE = "Target Support Files/SensorsAnalytics_DeviceOrientation/ResourceBundle-SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SensorsAnalytics_DeviceOrientation;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		CA547D2C7E9A8A153DC2B27FBE00B112 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		CBAC121D8144BF6FF05FFFE975D2AD10 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 53EA1965D0B7A605C8AAC0B1FC639594 /* SensorsAnalyticsSDK.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		D36439223E884E52DC81130A2259D069 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 659634383EF797BDFE4AC3E5CF4C7088 /* Pods-SensorsDataSwift.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-SensorsDataSwift/Pods-SensorsDataSwift-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-SensorsDataSwift/Pods-SensorsDataSwift.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		DCFE48FDA400B456916836408EC201F0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6DAFCCD24B12A5ED46ED83B957F5274F /* SensorsAnalyticsSDK.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		ECEDB06D1686123F0ED6813FB491C314 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1E59C13C69C28ED739D9F135B2DED211 /* SensorsAnalytics_DeviceOrientation.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F18702EBCFDB03CD4CF78A4249CD7A95 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2892535FCDC84B6EC0F4AEA376CB1E2 /* SensorsAnalytics_Location.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SensorsAnalytics_Location";
				IBSC_MODULE = SensorsAnalytics_Location;
				INFOPLIST_FILE = "Target Support Files/SensorsAnalytics_Location/ResourceBundle-SensorsAnalytics_Location-SensorsAnalytics_Location-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				PRODUCT_NAME = SensorsAnalytics_Location;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		F5570480B3AC91C1A8E80141508EC450 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B2892535FCDC84B6EC0F4AEA376CB1E2 /* SensorsAnalytics_Location.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		22DAF9DC1A2FDF6CFF4EF5E07F613EFE /* Build configuration list for PBXNativeTarget "SensorsAnalytics_Location-SensorsAnalytics_Location" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F18702EBCFDB03CD4CF78A4249CD7A95 /* Debug */,
				A0A0342EFDF0E50B733EB53F35CDBE95 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2AC68A15A3B4AFB0AAD18D7885097234 /* Build configuration list for PBXNativeTarget "Pods-SensorsData" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0FAAFA1606DC4D90105442161648EDD9 /* Debug */,
				5F34359A53D7C9EF3BD3DF9D5D5EDB1D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		465CED29B777299A9DD40166B09856DD /* Build configuration list for PBXNativeTarget "SensorsAnalyticsSDK-SensorsAnalyticsSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6CD9018A7BCC9E7CBF72BF706768AEF7 /* Debug */,
				5088F9FFA17EBA9A4E178008F727FE89 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				25AD9454612BF454A1E3DC4CD4FA8C6D /* Debug */,
				CA547D2C7E9A8A153DC2B27FBE00B112 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4AFD08FFD1B82D4F8E69430EA6E6782E /* Build configuration list for PBXAggregateTarget "SensorsAnalytics_DeviceOrientation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				47683FCAA5591A69CA67535AC8734FBA /* Debug */,
				ECEDB06D1686123F0ED6813FB491C314 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		686C28665D308BB26CFAD725B1662ABA /* Build configuration list for PBXAggregateTarget "SensorsAnalytics_Location" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F5570480B3AC91C1A8E80141508EC450 /* Debug */,
				7E6A009C1C5FCC5BFF8AF3F495563D9B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		6C4DCACF8619B5618D8C2DD7025E3B17 /* Build configuration list for PBXNativeTarget "Pods-SensorsDataSwift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D36439223E884E52DC81130A2259D069 /* Debug */,
				2D45D91400B224B01714B8DB8DDA3D81 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		894C51FA5CC2097CF51F537E0DE262D2 /* Build configuration list for PBXAggregateTarget "SensorsAnalyticsSDK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CBAC121D8144BF6FF05FFFE975D2AD10 /* Debug */,
				DCFE48FDA400B456916836408EC201F0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A6A271C89892EBBA04C3C897AB504D70 /* Build configuration list for PBXNativeTarget "SensorsAnalytics_DeviceOrientation-SensorsAnalytics_DeviceOrientation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9E8D0EC18366C68FC70AD34874C0CA8 /* Debug */,
				9D3C5FF72F57C693BE96C23761A44BBB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
