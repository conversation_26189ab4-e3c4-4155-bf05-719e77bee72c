{"name": "SensorsAnalyticsSDK", "version": "5.0.0", "summary": "The official iOS SDK of Sensors Analytics.", "homepage": "http://www.sensorsdata.cn", "source": {"git": "https://github.com/sensorsdata/sa-sdk-ios.git", "tag": "v5.0.0"}, "license": {"type": "Commercial", "file": "LICENSE"}, "authors": {"caojiang": "<EMAIL>"}, "default_subspecs": "Core", "frameworks": "Foundation", "libraries": ["icucore", "z"], "cocoapods_version": ">= 1.12.0", "platforms": {"ios": "9.0", "osx": "10.13", "tvos": "12.0", "watchos": "7.0"}, "subspecs": [{"name": "Core", "ios": {"frameworks": ["CoreTelephony", "SystemConfiguration", "WebKit", "UIKit"], "vendored_frameworks": "SensorsAnalyticsSDK/Source/Core/SensorsAnalyticsSDK.xcframework", "resource_bundles": {"SensorsAnalyticsSDK": "SensorsAnalyticsSDK/Resources/Core/**/*"}}, "watchos": {"frameworks": "WatchKit", "vendored_frameworks": "SensorsAnalyticsSDK/Source/Base/SensorsAnalyticsSDK.xcframework", "resource_bundles": {"SensorsAnalyticsSDK": "SensorsAnalyticsSDK/Resources/Base/**/*"}}, "osx": {"frameworks": ["SystemConfiguration", "WebKit"], "vendored_frameworks": "SensorsAnalyticsSDK/Source/Base/SensorsAnalyticsSDK.xcframework", "resource_bundles": {"SensorsAnalyticsSDK": "SensorsAnalyticsSDK/Resources/Base/**/*"}}, "tvos": {"frameworks": "SystemConfiguration", "vendored_frameworks": "SensorsAnalyticsSDK/Source/Base/SensorsAnalyticsSDK.xcframework", "resource_bundles": {"SensorsAnalyticsSDK": "SensorsAnalyticsSDK/Resources/Base/**/*"}}}, {"name": "Base", "ios": {"frameworks": ["CoreTelephony", "SystemConfiguration", "WebKit", "UIKit"]}, "watchos": {"frameworks": "WatchKit"}, "osx": {"frameworks": ["SystemConfiguration", "WebKit"]}, "tvos": {"frameworks": ["SystemConfiguration", "UIKit"]}, "vendored_frameworks": "SensorsAnalyticsSDK/Source/Base/SensorsAnalyticsSDK.xcframework", "resource_bundles": {"SensorsAnalyticsSDK": "SensorsAnalyticsSDK/Resources/Base/**/*"}}, {"name": "Exposure", "dependencies": {"SensorsAnalyticsSDK/Core": []}, "resource_bundles": {"SAExposureResources": "SensorsAnalyticsSDK/Resources/Exposure/*"}}, {"name": "EnglishResources", "dependencies": {"SensorsAnalyticsSDK/Core": []}, "resource_bundles": {"SAEnglishResources": "SensorsAnalyticsSDK/Resources/EnglishResources/*"}}]}