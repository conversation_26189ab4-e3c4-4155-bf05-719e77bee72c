<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SensorsAnalyticsSDK+DeviceOrientation.h</key>
		<data>
		KMcTQtTzZ1rQr0YADqfsLC6M6FI=
		</data>
		<key>Headers/SensorsAnalytics_DeviceOrientation-umbrella.h</key>
		<data>
		jY8o3MAZpoeJs3YoCWU177XWHjY=
		</data>
		<key>Headers/SensorsAnalytics_DeviceOrientation.h</key>
		<data>
		0Vmg/BsSaKhoinhDrs0UYVoH19U=
		</data>
		<key>Info.plist</key>
		<data>
		v5E8c0FyY7ERMDZeK9kcBmn0ddw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		JVAactWpGu0SHmp/ZyR5pMy9mZ8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SensorsAnalyticsSDK+DeviceOrientation.h</key>
		<dict>
			<key>hash</key>
			<data>
			KMcTQtTzZ1rQr0YADqfsLC6M6FI=
			</data>
			<key>hash2</key>
			<data>
			88V3Z/RwujiunEAvfWywbLRhkZtHRpVZDdqVNCPX+Y0=
			</data>
		</dict>
		<key>Headers/SensorsAnalytics_DeviceOrientation-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			jY8o3MAZpoeJs3YoCWU177XWHjY=
			</data>
			<key>hash2</key>
			<data>
			6bu71ZrYfEo85ojvfJ8hQLqpE7iXjZHTEZ6kPf1n+hU=
			</data>
		</dict>
		<key>Headers/SensorsAnalytics_DeviceOrientation.h</key>
		<dict>
			<key>hash</key>
			<data>
			0Vmg/BsSaKhoinhDrs0UYVoH19U=
			</data>
			<key>hash2</key>
			<data>
			NHZIvYRU1QvqXGmovSdvdjJE4dk/YrLMQdfP0+vuFQE=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			JVAactWpGu0SHmp/ZyR5pMy9mZ8=
			</data>
			<key>hash2</key>
			<data>
			+dHBgCle/Ydj/pH0cicV6ZGzUk/QaGFn15VKzUNWvO0=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
