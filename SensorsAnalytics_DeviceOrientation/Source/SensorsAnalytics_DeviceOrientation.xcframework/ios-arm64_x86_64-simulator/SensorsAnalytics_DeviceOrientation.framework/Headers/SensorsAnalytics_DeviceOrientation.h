//
//  SensorsAnalytics_DeviceOrientation.h
//  SensorsAnalytics_DeviceOrientation
//
//  Created by 陈玉国 on 2024/10/24.
//

#import <Foundation/Foundation.h>

//! Project version number for SensorsAnalytics_DeviceOrientation.
FOUNDATION_EXPORT double SensorsAnalytics_DeviceOrientationVersionNumber;

//! Project version string for SensorsAnalytics_DeviceOrientation.
FOUNDATION_EXPORT const unsigned char SensorsAnalytics_DeviceOrientationVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <SensorsAnalytics_DeviceOrientation/PublicHeader.h>

#import "SensorsAnalyticsSDK+DeviceOrientation.h"
