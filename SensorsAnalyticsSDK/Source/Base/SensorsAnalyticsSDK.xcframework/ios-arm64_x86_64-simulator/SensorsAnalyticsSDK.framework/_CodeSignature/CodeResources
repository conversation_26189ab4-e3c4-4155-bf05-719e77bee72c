<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SAAESStorePlugin.h</key>
		<data>
		3kQv6ESM5TlbnLM80TMpcLzTL2c=
		</data>
		<key>Headers/SABaseStoreManager.h</key>
		<data>
		90/rwYtaTXfhLkcEaEgge7ge5h4=
		</data>
		<key>Headers/SAConfigOptions.h</key>
		<data>
		R1UNuKPOFAxn+5PCrtuMOE7xSzE=
		</data>
		<key>Headers/SAConstants.h</key>
		<data>
		M4n+Gy9HX0NO8GG1EmSrmSZOhWU=
		</data>
		<key>Headers/SAModuleProtocol.h</key>
		<data>
		sbmAE2srRtNYOtaOphsQDES423A=
		</data>
		<key>Headers/SAPropertyPlugin.h</key>
		<data>
		/12tmEfBf/98HDn7d+amISTxmLk=
		</data>
		<key>Headers/SASecurityPolicy.h</key>
		<data>
		HV48iB4qHqZ1/+xqqSjR9Z0W+Gc=
		</data>
		<key>Headers/SAStorePlugin.h</key>
		<data>
		TS/s/dWhuVKBu6OFK+01qRqDMzE=
		</data>
		<key>Headers/SensorsAnalyticsExtension.h</key>
		<data>
		FYt0ZSnFMVDPx38UdbW9a/Hnb/U=
		</data>
		<key>Headers/SensorsAnalyticsSDK+JavaScriptBridge.h</key>
		<data>
		0fduhwGwGmKJQK4ABQQZEwiv1Mo=
		</data>
		<key>Headers/SensorsAnalyticsSDK+Public.h</key>
		<data>
		hTWh50U2pEBgnu4IDKqb4J/NWxM=
		</data>
		<key>Headers/SensorsAnalyticsSDK+SAAppExtension.h</key>
		<data>
		VJbpOCLkj8N/5Ttbr8Dzr8TsT1Q=
		</data>
		<key>Headers/SensorsAnalyticsSDK.h</key>
		<data>
		+7RY+L0A9gn2A0YG802DLmpsw4I=
		</data>
		<key>Info.plist</key>
		<data>
		9to0bkm6hRbeEfyYCCNZCNhJSrI=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		M28CzJQOMiZtnELWCLW+Ro8u83U=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SAAESStorePlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			3kQv6ESM5TlbnLM80TMpcLzTL2c=
			</data>
			<key>hash2</key>
			<data>
			sq+kPyus60+ntrdu7Vfu75+nuCPcqU6qsBsU72CpVNA=
			</data>
		</dict>
		<key>Headers/SABaseStoreManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			90/rwYtaTXfhLkcEaEgge7ge5h4=
			</data>
			<key>hash2</key>
			<data>
			5PVVlY4+SRmp0C8o2rTTxHLLY/cvb3VmpqSStcNtoOQ=
			</data>
		</dict>
		<key>Headers/SAConfigOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			R1UNuKPOFAxn+5PCrtuMOE7xSzE=
			</data>
			<key>hash2</key>
			<data>
			L1q3lCKKvHxrcTHrF/u46tfkQBLlV4pMK6/e3UDiJR8=
			</data>
		</dict>
		<key>Headers/SAConstants.h</key>
		<dict>
			<key>hash</key>
			<data>
			M4n+Gy9HX0NO8GG1EmSrmSZOhWU=
			</data>
			<key>hash2</key>
			<data>
			YqlEv0V21QiSt1Voc1ziHL4NL2pFkyZtmLFTUn3mjp8=
			</data>
		</dict>
		<key>Headers/SAModuleProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			sbmAE2srRtNYOtaOphsQDES423A=
			</data>
			<key>hash2</key>
			<data>
			OwtLWOBjnydpfaNm0ygth4YxY5k2F60vrnLscvSyEVY=
			</data>
		</dict>
		<key>Headers/SAPropertyPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			/12tmEfBf/98HDn7d+amISTxmLk=
			</data>
			<key>hash2</key>
			<data>
			2kcipVLA+wfoiNHJCMzDLwgQ67MOrB5KF5uPprYwoKk=
			</data>
		</dict>
		<key>Headers/SASecurityPolicy.h</key>
		<dict>
			<key>hash</key>
			<data>
			HV48iB4qHqZ1/+xqqSjR9Z0W+Gc=
			</data>
			<key>hash2</key>
			<data>
			Bz9Fct+PZuYxzyjKUsa+F292R15Bg9Q+CkPFwV9/os8=
			</data>
		</dict>
		<key>Headers/SAStorePlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			TS/s/dWhuVKBu6OFK+01qRqDMzE=
			</data>
			<key>hash2</key>
			<data>
			GbybiJUuLHJCeIbQsuPXomW7OMk9UYF+IUs+d37L+0g=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsExtension.h</key>
		<dict>
			<key>hash</key>
			<data>
			FYt0ZSnFMVDPx38UdbW9a/Hnb/U=
			</data>
			<key>hash2</key>
			<data>
			kFxvcTMGCj+pE64bdn/Io98sRGRQR3/CUXqzit52OF8=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+JavaScriptBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			0fduhwGwGmKJQK4ABQQZEwiv1Mo=
			</data>
			<key>hash2</key>
			<data>
			rx9kqQFY5XAC2tpbc/O4ZnCL8uUMUuFWr+txP6CT4/0=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+Public.h</key>
		<dict>
			<key>hash</key>
			<data>
			hTWh50U2pEBgnu4IDKqb4J/NWxM=
			</data>
			<key>hash2</key>
			<data>
			WVW9rUYAwOyIwiyVSZCZjhXnkqpOg9p2F/YHHJref94=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+SAAppExtension.h</key>
		<dict>
			<key>hash</key>
			<data>
			VJbpOCLkj8N/5Ttbr8Dzr8TsT1Q=
			</data>
			<key>hash2</key>
			<data>
			SNcWedFenHl3EkbzGnS2wD3i05nWNFPg06L1mILrIcA=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			+7RY+L0A9gn2A0YG802DLmpsw4I=
			</data>
			<key>hash2</key>
			<data>
			fBZpZ5GYsBOfG3RbQlkS0m/l9BoOSthb1hjVYjBxlyk=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			M28CzJQOMiZtnELWCLW+Ro8u83U=
			</data>
			<key>hash2</key>
			<data>
			6vxhwLv7GzZzb3zUcTpO9IVhuNewN38bHnWMV0l/1Ps=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
