//
// SensorsAnalyticsSDK.h
// SensorsAnalyticsSDK
//
// Created by 曹犟 on 15/7/1.
// Copyright © 2015-2022 Sensors Data Co., Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "SensorsAnalyticsSDK+Public.h"
#import "SASecurityPolicy.h"
#import "SAConfigOptions.h"
#import "SAConstants.h"


//SensorsAnalyticsSDK section
#if __has_include("SensorsAnalyticsSDK+SAChannelMatch.h")
#import "SensorsAnalyticsSDK+SAChannelMatch.h"
#endif

#if __has_include("SensorsAnalyticsSDK+DebugMode.h")
#import "SensorsAnalyticsSDK+DebugMode.h"
#endif

#if __has_include("SensorsAnalyticsSDK+DeepLink.h")
#import "SensorsAnalyticsSDK+DeepLink.h"
#import "SAAdvertisingConfig.h"
#endif

#if __has_include("SensorsAnalyticsSDK+SAAutoTrack.h")
#import "SensorsAnalyticsSDK+SAAutoTrack.h"
#endif

#if __has_include("SensorsAnalyticsSDK+Visualized.h")
#import "SensorsAnalyticsSDK+Visualized.h"
#endif

#if __has_include("SASecretKey.h")
#import "SASecretKey.h"
#endif

#if __has_include("SensorsAnalyticsSDK+JavaScriptBridge.h")
#import "SensorsAnalyticsSDK+JavaScriptBridge.h"
#endif


//configOptions section

#if __has_include("SAConfigOptions+RemoteConfig.h")
#import "SAConfigOptions+RemoteConfig.h"
#endif

#if __has_include("SAConfigOptions+Encrypt.h")
#import "SAConfigOptions+Encrypt.h"
#endif

#if __has_include("SAConfigOptions+AppPush.h")
#import "SAConfigOptions+AppPush.h"
#endif

#if __has_include("SAConfigOptions+Exception.h")
#import "SAConfigOptions+Exception.h"
#endif


#if __has_include("SensorsAnalyticsSDK+WKWebView.h")
#import "SensorsAnalyticsSDK+WKWebView.h"
#endif

#if __has_include("SensorsAnalyticsSDK+WebView.h")
#import "SensorsAnalyticsSDK+WebView.h"
#endif

#if __has_include("SensorsAnalyticsSDK+SAAppExtension.h")
#import "SensorsAnalyticsSDK+SAAppExtension.h"
#endif

#if __has_include("SAConfigOptions+Exposure.h")
#import "SAConfigOptions+Exposure.h"
#endif

#if __has_include("UIView+SensorsAnalytics.h")
#import "UIView+SensorsAnalytics.h"
#endif


#import "SAAESStorePlugin.h"
#import "SAModuleProtocol.h"
