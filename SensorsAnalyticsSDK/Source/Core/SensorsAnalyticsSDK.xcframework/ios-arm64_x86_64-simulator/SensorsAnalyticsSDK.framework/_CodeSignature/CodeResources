<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SAAESStorePlugin.h</key>
		<data>
		3kQv6ESM5TlbnLM80TMpcLzTL2c=
		</data>
		<key>Headers/SAAdvertisingConfig.h</key>
		<data>
		qP7TzVl0KBHCN50jEaxrAkJKDko=
		</data>
		<key>Headers/SABaseStoreManager.h</key>
		<data>
		90/rwYtaTXfhLkcEaEgge7ge5h4=
		</data>
		<key>Headers/SAConfigOptions+AppPush.h</key>
		<data>
		AcQqDVSx0vhl+H0ih2sVYMz0QSw=
		</data>
		<key>Headers/SAConfigOptions+Encrypt.h</key>
		<data>
		Qw5maWS0C47RmWhKGXDFtZWemY8=
		</data>
		<key>Headers/SAConfigOptions+Exception.h</key>
		<data>
		XMQnoERMbvC5HzX84NacHg1Cfy8=
		</data>
		<key>Headers/SAConfigOptions+Exposure.h</key>
		<data>
		+mgzZWQJoVL+bU7kTB8ykbTgdVg=
		</data>
		<key>Headers/SAConfigOptions+RemoteConfig.h</key>
		<data>
		4dAfXL1rxcfBdcH7IUL9qASj7qk=
		</data>
		<key>Headers/SAConfigOptions.h</key>
		<data>
		R1UNuKPOFAxn+5PCrtuMOE7xSzE=
		</data>
		<key>Headers/SAConstants.h</key>
		<data>
		M4n+Gy9HX0NO8GG1EmSrmSZOhWU=
		</data>
		<key>Headers/SAEncryptProtocol.h</key>
		<data>
		w3+B11dahpVpD33CIN6DGpor6t4=
		</data>
		<key>Headers/SAExposureConfig.h</key>
		<data>
		oi5wqhiCw+gORCwQEX6//mQHt7o=
		</data>
		<key>Headers/SAExposureData.h</key>
		<data>
		eT5TC/SbblaNof7h6TehOXbx2zs=
		</data>
		<key>Headers/SAExposureListener.h</key>
		<data>
		1hfs2bqPsT5ZF42oKjViDsKcqeQ=
		</data>
		<key>Headers/SAModuleProtocol.h</key>
		<data>
		sbmAE2srRtNYOtaOphsQDES423A=
		</data>
		<key>Headers/SAPropertyPlugin.h</key>
		<data>
		/12tmEfBf/98HDn7d+amISTxmLk=
		</data>
		<key>Headers/SASecretKey.h</key>
		<data>
		a3jM2/N47InOdFo5lxtGZ1fQZ5Y=
		</data>
		<key>Headers/SASecurityPolicy.h</key>
		<data>
		HV48iB4qHqZ1/+xqqSjR9Z0W+Gc=
		</data>
		<key>Headers/SASlinkCreator.h</key>
		<data>
		E3PnkF6n1OBtkxSoxUq+su7gNZY=
		</data>
		<key>Headers/SAStorePlugin.h</key>
		<data>
		TS/s/dWhuVKBu6OFK+01qRqDMzE=
		</data>
		<key>Headers/SensorsAnalyticsExtension.h</key>
		<data>
		FYt0ZSnFMVDPx38UdbW9a/Hnb/U=
		</data>
		<key>Headers/SensorsAnalyticsSDK+DebugMode.h</key>
		<data>
		uUMNSqrB/GzoxcwmjAjOwwwLJhY=
		</data>
		<key>Headers/SensorsAnalyticsSDK+DeepLink.h</key>
		<data>
		7KNM5zBRR4Cbc46kXD98Qi84LZI=
		</data>
		<key>Headers/SensorsAnalyticsSDK+Exposure.h</key>
		<data>
		qwWBSSDU4Ect9frj6GawwcUaRaY=
		</data>
		<key>Headers/SensorsAnalyticsSDK+JavaScriptBridge.h</key>
		<data>
		0fduhwGwGmKJQK4ABQQZEwiv1Mo=
		</data>
		<key>Headers/SensorsAnalyticsSDK+Public.h</key>
		<data>
		hTWh50U2pEBgnu4IDKqb4J/NWxM=
		</data>
		<key>Headers/SensorsAnalyticsSDK+SAAppExtension.h</key>
		<data>
		VJbpOCLkj8N/5Ttbr8Dzr8TsT1Q=
		</data>
		<key>Headers/SensorsAnalyticsSDK+SAAutoTrack.h</key>
		<data>
		1nbUfNA3qMERuX5UXqxvqg1ey/I=
		</data>
		<key>Headers/SensorsAnalyticsSDK+SAChannelMatch.h</key>
		<data>
		Wzaj6eLjxBPjuOozqwWOYH0HWpQ=
		</data>
		<key>Headers/SensorsAnalyticsSDK+Visualized.h</key>
		<data>
		0nzI4tvZMv5tVNTVgVGZ9YOuviY=
		</data>
		<key>Headers/SensorsAnalyticsSDK.h</key>
		<data>
		+7RY+L0A9gn2A0YG802DLmpsw4I=
		</data>
		<key>Headers/UIView+ExposureIdentifier.h</key>
		<data>
		g2PPkMgwlhEa9DluATuEtYVtDLk=
		</data>
		<key>Headers/UIView+SensorsAnalytics.h</key>
		<data>
		UP+ddkd4sARigJOWfdOb+8dqOZ0=
		</data>
		<key>Info.plist</key>
		<data>
		4Vkm2FiotLH410h9IoSdXBSGUH0=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		M28CzJQOMiZtnELWCLW+Ro8u83U=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SAAESStorePlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			3kQv6ESM5TlbnLM80TMpcLzTL2c=
			</data>
			<key>hash2</key>
			<data>
			sq+kPyus60+ntrdu7Vfu75+nuCPcqU6qsBsU72CpVNA=
			</data>
		</dict>
		<key>Headers/SAAdvertisingConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			qP7TzVl0KBHCN50jEaxrAkJKDko=
			</data>
			<key>hash2</key>
			<data>
			1dPobmZuOdjTcwa55nyQklL0eo6KZLYwLcaI6HWEO+Q=
			</data>
		</dict>
		<key>Headers/SABaseStoreManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			90/rwYtaTXfhLkcEaEgge7ge5h4=
			</data>
			<key>hash2</key>
			<data>
			5PVVlY4+SRmp0C8o2rTTxHLLY/cvb3VmpqSStcNtoOQ=
			</data>
		</dict>
		<key>Headers/SAConfigOptions+AppPush.h</key>
		<dict>
			<key>hash</key>
			<data>
			AcQqDVSx0vhl+H0ih2sVYMz0QSw=
			</data>
			<key>hash2</key>
			<data>
			55EG+hCiD1a/jugeZ4gzv0LLwceh+N4+mGJo9K3TF1w=
			</data>
		</dict>
		<key>Headers/SAConfigOptions+Encrypt.h</key>
		<dict>
			<key>hash</key>
			<data>
			Qw5maWS0C47RmWhKGXDFtZWemY8=
			</data>
			<key>hash2</key>
			<data>
			nBS/8Z3HavpAjh3mP7b6EwqcN/MlhLphukv305fKcqM=
			</data>
		</dict>
		<key>Headers/SAConfigOptions+Exception.h</key>
		<dict>
			<key>hash</key>
			<data>
			XMQnoERMbvC5HzX84NacHg1Cfy8=
			</data>
			<key>hash2</key>
			<data>
			SRwqEuvWvTYFhRIb/I0CXd83t8uMABtGeqc7rM23jR8=
			</data>
		</dict>
		<key>Headers/SAConfigOptions+Exposure.h</key>
		<dict>
			<key>hash</key>
			<data>
			+mgzZWQJoVL+bU7kTB8ykbTgdVg=
			</data>
			<key>hash2</key>
			<data>
			9w7RQtyDOAZ+F7X95es1j5kL48CZ6KNHPUU75b1AH+Y=
			</data>
		</dict>
		<key>Headers/SAConfigOptions+RemoteConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			4dAfXL1rxcfBdcH7IUL9qASj7qk=
			</data>
			<key>hash2</key>
			<data>
			w2lshlWZIghm5sQ1uiXQZkbwQmrzaVrsCrSF/cBwroQ=
			</data>
		</dict>
		<key>Headers/SAConfigOptions.h</key>
		<dict>
			<key>hash</key>
			<data>
			R1UNuKPOFAxn+5PCrtuMOE7xSzE=
			</data>
			<key>hash2</key>
			<data>
			L1q3lCKKvHxrcTHrF/u46tfkQBLlV4pMK6/e3UDiJR8=
			</data>
		</dict>
		<key>Headers/SAConstants.h</key>
		<dict>
			<key>hash</key>
			<data>
			M4n+Gy9HX0NO8GG1EmSrmSZOhWU=
			</data>
			<key>hash2</key>
			<data>
			YqlEv0V21QiSt1Voc1ziHL4NL2pFkyZtmLFTUn3mjp8=
			</data>
		</dict>
		<key>Headers/SAEncryptProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			w3+B11dahpVpD33CIN6DGpor6t4=
			</data>
			<key>hash2</key>
			<data>
			aFXmyCFfBy/Y8UoeXMuJajwNDGo0v7ojtmJk5VTSsKM=
			</data>
		</dict>
		<key>Headers/SAExposureConfig.h</key>
		<dict>
			<key>hash</key>
			<data>
			oi5wqhiCw+gORCwQEX6//mQHt7o=
			</data>
			<key>hash2</key>
			<data>
			JPQwHqpL9MmWVKWy5Q77ZzNP6MFNYfi1CiK2AYkaXnQ=
			</data>
		</dict>
		<key>Headers/SAExposureData.h</key>
		<dict>
			<key>hash</key>
			<data>
			eT5TC/SbblaNof7h6TehOXbx2zs=
			</data>
			<key>hash2</key>
			<data>
			ySAILNfp8/xRRiOI0VjlKJ1UHFeoEyybT+hq0ji7gtg=
			</data>
		</dict>
		<key>Headers/SAExposureListener.h</key>
		<dict>
			<key>hash</key>
			<data>
			1hfs2bqPsT5ZF42oKjViDsKcqeQ=
			</data>
			<key>hash2</key>
			<data>
			E5QS9nu5Tjkz6ldnSXwOGBelnEIc4EEHz+HAGgPnt0o=
			</data>
		</dict>
		<key>Headers/SAModuleProtocol.h</key>
		<dict>
			<key>hash</key>
			<data>
			sbmAE2srRtNYOtaOphsQDES423A=
			</data>
			<key>hash2</key>
			<data>
			OwtLWOBjnydpfaNm0ygth4YxY5k2F60vrnLscvSyEVY=
			</data>
		</dict>
		<key>Headers/SAPropertyPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			/12tmEfBf/98HDn7d+amISTxmLk=
			</data>
			<key>hash2</key>
			<data>
			2kcipVLA+wfoiNHJCMzDLwgQ67MOrB5KF5uPprYwoKk=
			</data>
		</dict>
		<key>Headers/SASecretKey.h</key>
		<dict>
			<key>hash</key>
			<data>
			a3jM2/N47InOdFo5lxtGZ1fQZ5Y=
			</data>
			<key>hash2</key>
			<data>
			j0LnH0Jb2h8r3CZpkE7jXrkeXmbKelkRGmOE28ZHFjY=
			</data>
		</dict>
		<key>Headers/SASecurityPolicy.h</key>
		<dict>
			<key>hash</key>
			<data>
			HV48iB4qHqZ1/+xqqSjR9Z0W+Gc=
			</data>
			<key>hash2</key>
			<data>
			Bz9Fct+PZuYxzyjKUsa+F292R15Bg9Q+CkPFwV9/os8=
			</data>
		</dict>
		<key>Headers/SASlinkCreator.h</key>
		<dict>
			<key>hash</key>
			<data>
			E3PnkF6n1OBtkxSoxUq+su7gNZY=
			</data>
			<key>hash2</key>
			<data>
			YGuYtPq538JU+8xFdvvfIg/s7ght2bYAKO4YIwXruAs=
			</data>
		</dict>
		<key>Headers/SAStorePlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			TS/s/dWhuVKBu6OFK+01qRqDMzE=
			</data>
			<key>hash2</key>
			<data>
			GbybiJUuLHJCeIbQsuPXomW7OMk9UYF+IUs+d37L+0g=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsExtension.h</key>
		<dict>
			<key>hash</key>
			<data>
			FYt0ZSnFMVDPx38UdbW9a/Hnb/U=
			</data>
			<key>hash2</key>
			<data>
			kFxvcTMGCj+pE64bdn/Io98sRGRQR3/CUXqzit52OF8=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+DebugMode.h</key>
		<dict>
			<key>hash</key>
			<data>
			uUMNSqrB/GzoxcwmjAjOwwwLJhY=
			</data>
			<key>hash2</key>
			<data>
			oKNxFp04UgPE+K8J1CnfJI4NEqI1HDmQ5SdnCV/aX8A=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+DeepLink.h</key>
		<dict>
			<key>hash</key>
			<data>
			7KNM5zBRR4Cbc46kXD98Qi84LZI=
			</data>
			<key>hash2</key>
			<data>
			uGu7qZJP8OtEl45Nh2Hu3zVsM1xRmn3o7H+47aGNnGg=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+Exposure.h</key>
		<dict>
			<key>hash</key>
			<data>
			qwWBSSDU4Ect9frj6GawwcUaRaY=
			</data>
			<key>hash2</key>
			<data>
			E9EEOFZoHhoqi7nnV6aZn0ieF971PIxXsUJrw/lspnU=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+JavaScriptBridge.h</key>
		<dict>
			<key>hash</key>
			<data>
			0fduhwGwGmKJQK4ABQQZEwiv1Mo=
			</data>
			<key>hash2</key>
			<data>
			rx9kqQFY5XAC2tpbc/O4ZnCL8uUMUuFWr+txP6CT4/0=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+Public.h</key>
		<dict>
			<key>hash</key>
			<data>
			hTWh50U2pEBgnu4IDKqb4J/NWxM=
			</data>
			<key>hash2</key>
			<data>
			WVW9rUYAwOyIwiyVSZCZjhXnkqpOg9p2F/YHHJref94=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+SAAppExtension.h</key>
		<dict>
			<key>hash</key>
			<data>
			VJbpOCLkj8N/5Ttbr8Dzr8TsT1Q=
			</data>
			<key>hash2</key>
			<data>
			SNcWedFenHl3EkbzGnS2wD3i05nWNFPg06L1mILrIcA=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+SAAutoTrack.h</key>
		<dict>
			<key>hash</key>
			<data>
			1nbUfNA3qMERuX5UXqxvqg1ey/I=
			</data>
			<key>hash2</key>
			<data>
			PYAXlBvsb09ykBco/zEi9/LH/KNPkRx72Kwfka6de7k=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+SAChannelMatch.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wzaj6eLjxBPjuOozqwWOYH0HWpQ=
			</data>
			<key>hash2</key>
			<data>
			I6BrVC0rDkIr/ZIgNmSzTasNMqbyyCJtJoYRB6XIONI=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK+Visualized.h</key>
		<dict>
			<key>hash</key>
			<data>
			0nzI4tvZMv5tVNTVgVGZ9YOuviY=
			</data>
			<key>hash2</key>
			<data>
			JXRSnfH8m0GSzyEGzFMiZs8RUO8sFIAcxIOKz1LfFko=
			</data>
		</dict>
		<key>Headers/SensorsAnalyticsSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			+7RY+L0A9gn2A0YG802DLmpsw4I=
			</data>
			<key>hash2</key>
			<data>
			fBZpZ5GYsBOfG3RbQlkS0m/l9BoOSthb1hjVYjBxlyk=
			</data>
		</dict>
		<key>Headers/UIView+ExposureIdentifier.h</key>
		<dict>
			<key>hash</key>
			<data>
			g2PPkMgwlhEa9DluATuEtYVtDLk=
			</data>
			<key>hash2</key>
			<data>
			gAc5erutJQCcm5ekKDjJycMzVVJqpPfqbgua/jId5Ew=
			</data>
		</dict>
		<key>Headers/UIView+SensorsAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			UP+ddkd4sARigJOWfdOb+8dqOZ0=
			</data>
			<key>hash2</key>
			<data>
			gHzbLA2TELAN0UbaWI+bFWkJtM43Mgwpvzbgc+bncS0=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			M28CzJQOMiZtnELWCLW+Ro8u83U=
			</data>
			<key>hash2</key>
			<data>
			6vxhwLv7GzZzb3zUcTpO9IVhuNewN38bHnWMV0l/1Ps=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
