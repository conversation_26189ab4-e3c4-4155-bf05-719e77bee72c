<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SensorsAnalyticsSDK+Location.h</key>
		<data>
		45UbWbmtRUXYdnuKPNI1lAIKnTA=
		</data>
		<key>Headers/SensorsAnalytics_Location-umbrella.h</key>
		<data>
		LTHWwkB3sBeKUuLdyLiJZ7f+8sc=
		</data>
		<key>Headers/SensorsAnalytics_Location.h</key>
		<data>
		sG34q1b3WGFJFOr39VTv9qiTs2I=
		</data>
		<key>Info.plist</key>
		<data>
		d4Einr+z94bu9Ru9nRDIdPO0gWw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		v3cweNcb1vQsCzGhBux5DI3CPGo=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SensorsAnalyticsSDK+Location.h</key>
		<dict>
			<key>hash</key>
			<data>
			45UbWbmtRUXYdnuKPNI1lAIKnTA=
			</data>
			<key>hash2</key>
			<data>
			eDkPVkm7YG2T16XtqwVmqHmAzbst93NUVSxejCVFf0w=
			</data>
		</dict>
		<key>Headers/SensorsAnalytics_Location-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			LTHWwkB3sBeKUuLdyLiJZ7f+8sc=
			</data>
			<key>hash2</key>
			<data>
			+q46rry2l9Eb9D00zQu+YUklLnS53KpFyxKMF0f7kqs=
			</data>
		</dict>
		<key>Headers/SensorsAnalytics_Location.h</key>
		<dict>
			<key>hash</key>
			<data>
			sG34q1b3WGFJFOr39VTv9qiTs2I=
			</data>
			<key>hash2</key>
			<data>
			wqPB6+ZM/DKvctQFtUeyc84dRhIrcoXzWyKYtzAB8cM=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			v3cweNcb1vQsCzGhBux5DI3CPGo=
			</data>
			<key>hash2</key>
			<data>
			oVv1Xv0FWp+ls/AFYo0673KyAOtrIyytz+QCK8rBobs=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
