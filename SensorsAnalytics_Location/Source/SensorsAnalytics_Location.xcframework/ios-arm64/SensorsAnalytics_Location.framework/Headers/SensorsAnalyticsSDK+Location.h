//
// SensorsAnalyticsSDK+Location.h
// SensorsAnalyticsSDK
//
// Created by 陈玉国 on 2021/9/11.
// Copyright © 2015-2022 Sensors Data Co., Ltd. All rights reserved.
//

#import <SensorsAnalyticsSDK/SensorsAnalyticsSDK.h>

NS_ASSUME_NONNULL_BEGIN

@interface SensorsAnalyticsSDK (Location)

/**
 * @abstract
 * 位置信息采集功能开关
 *
 * @discussion
 * 根据需要决定是否开启位置采集
 * 默认关闭
 *
 * @param enable YES/NO
 */
- (void)enableTrackGPSLocation:(BOOL)enable API_UNAVAILABLE(macos) NS_EXTENSION_UNAVAILABLE("Location not supported for iOS extensions.");

@end

NS_ASSUME_NONNULL_END
