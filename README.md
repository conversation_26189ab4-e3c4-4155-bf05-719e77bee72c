<img src="https://ow-file.sensorsdata.cn/www/home/<USER>/sensors_header_icon.svg" width="200" >

[![License](https://img.shields.io/github/license/sensorsdata/sa-sdk-ios.svg)](https://github.com/sensorsdata/sa-sdk-ios/blob/master/LICENSE)
[![Average time to resolve an issue](http://isitmaintained.com/badge/resolution/sensorsdata/sa-sdk-ios.svg)](http://isitmaintained.com/project/sensorsdata/sa-sdk-ios "Average time to resolve an issue")
[![Percentage of issues still open](http://isitmaintained.com/badge/open/sensorsdata/sa-sdk-ios.svg)](http://isitmaintained.com/project/sensorsdata/sa-sdk-ios "Percentage of issues still open")
[![GitHub release](https://img.shields.io/github/tag/sensorsdata/sa-sdk-ios.svg?label=release)](https://github.com/sensorsdata/sa-sdk-ios/releases)
[![GitHub release date](https://img.shields.io/github/release-date/sensorsdata/sa-sdk-ios.svg)](https://github.com/sensorsdata/sa-sdk-ios/releases)
[![Carthage Compatible](https://img.shields.io/badge/Carthage-compatible-4BC51D.svg?style=flat)](https://github.com/Carthage/Carthage)
[![CocoaPods Compatible](https://img.shields.io/cocoapods/v/SensorsAnalyticsSDK.svg)](https://img.shields.io/cocoapods/v/SensorsAnalyticsSDK.svg)
[![Platform](https://img.shields.io/cocoapods/p/SensorsAnalyticsSDK.svg?style=flat)](http://cocoadocs.org/docsets/SensorsAnalyticsSDK)

## 神策简介

[**神策数据**](https://www.sensorsdata.cn/)
（Sensors Data），隶属于神策网络科技（北京）有限公司，是一家专业的大数据分析服务公司，大数据分析行业开拓者，为客户提供深度用户行为分析平台、以及专业的咨询服务和行业解决方案，致力于帮助客户实现数据驱动。神策数据立足大数据及用户行为分析的技术与实践前沿，业务现已覆盖以互联网、金融、零售快消、高科技、制造等为代表的十多个主要行业、并可支持企业多个职能部门。公司总部在北京，并在上海、深圳、合肥、武汉等地拥有本地化的服务团队，覆盖东区及南区市场；公司拥有专业的服务团队，为客户提供一对一的客户服务。公司在大数据领域积累的核心关键技术，包括在海量数据采集、存储、清洗、分析挖掘、可视化、智能应用、安全与隐私保护等领域。 [**More**](https://www.sensorsdata.cn/about/aboutus.html)


## SDK 简介

SensorsAnalytics SDK 是国内第一家开源商用版用户行为采集 SDK，目前支持代码埋点、全埋点、App 点击图、可视化全埋点等。目前已累计有 1500 多家付费客户，2500+ 的 App 集成使用，作为 App 数据采集利器，致力于帮助客户挖掘更多的商业价值，为其精准运营和业务支撑提供了可靠的数据来源。其采集全面而灵活、性能良好，并一直保持稳定的迭代，经受住了时间和客户的考验。

## 基本要求

iOS 9.0 及以上，Xcode 9.0 及以上。

## 推荐版本

| 推荐版本 | 版本链接 |
| ------ | ------ | 
| v4.5.7 | https://github.com/sensorsdata/sa-sdk-ios/releases/tag/v4.5.7  |
| v4.4.5 | https://github.com/sensorsdata/sa-sdk-ios/releases/tag/v4.4.5  |

## 集成文档

请参考神策官网 [iOS SDK 集成文档](http://www.sensorsdata.cn/manual/ios_sdk.html)。


## 贡献

* 1.  在您的 GitHub 账户下 fork sa-sdk-ios 开源项目；
* 2.  根据您的需求在本地 clone 一份 sa-sdk-ios 源码；
* 3.  您修改或者新增功能后，push 到您 fork 的远程分支；
* 4.  创建 pull request，向 sa-sdk-ios 官方开发分支提交合入请求；
* 5.  神策 SDK 研发团队会及时 review 代码，测试通过后合入。

## 规划

可参考 [ROADMAP](ROADMAP.md).


## 新书推荐

| [《数据驱动：从方法到实践》](https://item.jd.com/12322322.html) | [《Android 全埋点解决方案》](https://item.jd.com/12574672.html) | [《iOS 全埋点解决方案》](https://item.jd.com/12867068.html)
| ------ | ------ | ------ |


## 感谢
- [mixpanel-iphone](https://github.com/mixpanel/mixpanel-iphone) 
- [TiDB](https://github.com/pingcap/tidb) 
- [Knight-ZXW](https://github.com/Knight-ZXW)

## License

[License 协议](https://github.com/sensorsdata/sa-sdk-ios/blob/master/LICENSE)
